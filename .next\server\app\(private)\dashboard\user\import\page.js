(()=>{var a={};a.id=286,a.ids=[286],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6508:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(95916),f=c(17396);let g=()=>(0,d.jsxs)(f.default,{requiredPermission:"user_import_csv",children:[(0,d.jsx)("h1",{className:"text-2xl",children:" Nhập nhanh th\xe0nh vi\xean từ File CSV"}),(0,d.jsx)(e.default,{})]})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16636:(a,b,c)=>{Promise.resolve().then(c.bind(c,95916)),Promise.resolve().then(c.bind(c,17396))},17396:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\components\\\\PermissionGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\components\\PermissionGuard.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25806:()=>{},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29310:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(60687),e=c(43210),f=c(84430),g=c.n(f),h=c(93853);c(25806);var i=c(88577);let j=()=>{let[a,b]=(0,e.useState)(null),[c,f]=(0,e.useState)(!1),[j,k]=(0,e.useState)([]),[l,m]=(0,e.useState)(0),[n,o]=(0,e.useState)(0),[p,q]=(0,e.useState)(0),[r,s]=(0,e.useState)([]),t=async()=>{if(0===j.length)return void h.oR.warn("No data to upload!");f(!0),s([]),o(0),q(0);let a=0,b=0,c=Math.min(j.length,100);for(let d=0;d<c;d++){let e="";try{let c=localStorage.getItem("sessionToken")||"",f=await i.A.CreateUser(j[d],c);f.payload.success?(a++,e="Success"):(b++,e=`Error: ${f.payload.message||"Unknown error"}`)}catch(a){b++,e=a.payload.message}o(a),q(b),m(Math.round((d+1)/c*100)),s(a=>[...a,{...j[d],status:e}]),d<c-1&&await new Promise(a=>setTimeout(a,2e3))}f(!1),h.oR.success("Import completed!")};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"mt-4 p-4 border rounded border-dashed",children:(0,d.jsx)("input",{type:"file",accept:".csv",onChange:a=>{a.target.files?.[0]&&b(a.target.files[0])}})}),(0,d.jsxs)("div",{className:"my-4 flex gap-4",children:[(0,d.jsx)("button",{className:"btn btn-md",onClick:()=>{a&&g().parse(a,{header:!0,skipEmptyLines:!0,complete:a=>{if(0===a.data.length)return void h.oR.error("No valid data found in the file!");let b=[],c=0;a.data.forEach(a=>{a.username&&a.email&&a.password?b.length<100&&b.push(a):c++}),k(b),o(0),q(c),s([]),h.oR.success(`Ph\xe2n t\xedch: ${b.length} hợp lệ, ${c} kh\xf4ng hợp lệ`)},error:()=>{h.oR.error("Failed to parse CSV file!")}})},disabled:!a,children:"Kiểm tra File"}),(0,d.jsx)("button",{className:"btn btn-md btn-primary",onClick:t,disabled:c||0===j.length,children:c?`Đang tải... ${l}%`:"Import Dữ Liệu"})]}),(n+p>0||r.length>0)&&(0,d.jsxs)("div",{className:"mt-4 border p-4 rounded bg-gray-100",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Kết quả Import"}),(0,d.jsxs)("p",{children:["Dữ liệu th\xe0nh c\xf4ng:"," ",(0,d.jsx)("span",{className:"text-green-600 font-bold",children:n})]}),(0,d.jsxs)("p",{children:["Dữ liệu lỗi:"," ",(0,d.jsx)("span",{className:"text-red-600 font-bold",children:p})]}),(0,d.jsxs)("p",{children:["Tiến độ:"," ",(0,d.jsxs)("span",{className:"text-blue-600 font-bold",children:[l,"%"]})]})]}),r.length>0&&(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsxs)("table",{className:"table-auto w-full border border-collapse mt-4",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"bg-gray-200",children:[(0,d.jsx)("th",{className:"border px-2 py-1",children:"Username"}),(0,d.jsx)("th",{className:"border px-2 py-1",children:"Email"}),(0,d.jsx)("th",{className:"border px-2 py-1",children:"Phone Number"}),(0,d.jsx)("th",{className:"border px-2 py-1",children:"Status"})]})}),(0,d.jsx)("tbody",{children:r.map((a,b)=>(0,d.jsxs)("tr",{className:"border",children:[(0,d.jsx)("td",{className:"border px-2 py-1",children:a.username}),(0,d.jsx)("td",{className:"border px-2 py-1",children:a.email}),(0,d.jsx)("td",{className:"border px-2 py-1",children:a.phonenumber}),(0,d.jsx)("td",{className:`border px-2 py-1 ${a.status.includes("Success")?"text-green-600":"text-red-600"}`,children:a.status})]},b))})]})})]})}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71449:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["user",{children:["import",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,6508)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\user\\import\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\user\\import\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/user/import/page",pathname:"/dashboard/user/import",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/user/import/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},74780:(a,b,c)=>{Promise.resolve().then(c.bind(c,29310)),Promise.resolve().then(c.bind(c,98462))},79428:a=>{"use strict";a.exports=require("buffer")},84430:function(a,b,c){var d,e;void 0===(e="function"==typeof(d=function a(){"use strict";var b="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==b?b:{},d=!b.document&&!!b.postMessage,e=b.IS_PAPA_WORKER||!1,f={},g=0,h={};if(h.parse=function(c,d){var e,i=(d=d||{}).dynamicTyping||!1;if(x(i)&&(d.dynamicTypingFunction=i,i={}),d.dynamicTyping=i,d.transform=!!x(d.transform)&&d.transform,d.worker&&h.WORKERS_SUPPORTED){var j=function(){if(!h.WORKERS_SUPPORTED)return!1;var c,d,e=(c=b.URL||b.webkitURL||null,d=a.toString(),h.BLOB_URL||(h.BLOB_URL=c.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",d,")();"],{type:"text/javascript"})))),i=new b.Worker(e);return i.onmessage=s,i.id=g++,f[i.id]=i,i}();j.userStep=d.step,j.userChunk=d.chunk,j.userComplete=d.complete,j.userError=d.error,d.step=x(d.step),d.chunk=x(d.chunk),d.complete=x(d.complete),d.error=x(d.error),delete d.worker,j.postMessage({input:c,config:d,workerId:j.id});return}var p=null;return c===h.NODE_STREAM_INPUT&&"undefined"==typeof PAPA_BROWSER_CONTEXT?(p=new o(d)).getStream():("string"==typeof c?(c=65279===(e=c).charCodeAt(0)?e.slice(1):e,p=d.download?new k(d):new m(d)):!0===c.readable&&x(c.read)&&x(c.on)?p=new n(d):(b.File&&c instanceof File||c instanceof Object)&&(p=new l(d)),p.stream(c))},h.unparse=function(a,b){var c=!1,d=!0,e=",",f="\r\n",g='"',i=g+g,j=!1,k=null,l=!1;if("object"==typeof b){if("string"!=typeof b.delimiter||h.BAD_DELIMITERS.filter(function(a){return -1!==b.delimiter.indexOf(a)}).length||(e=b.delimiter),("boolean"==typeof b.quotes||"function"==typeof b.quotes||Array.isArray(b.quotes))&&(c=b.quotes),("boolean"==typeof b.skipEmptyLines||"string"==typeof b.skipEmptyLines)&&(j=b.skipEmptyLines),"string"==typeof b.newline&&(f=b.newline),"string"==typeof b.quoteChar&&(g=b.quoteChar),"boolean"==typeof b.header&&(d=b.header),Array.isArray(b.columns)){if(0===b.columns.length)throw Error("Option columns is empty");k=b.columns}void 0!==b.escapeChar&&(i=b.escapeChar+g),b.escapeFormulae instanceof RegExp?l=b.escapeFormulae:"boolean"==typeof b.escapeFormulae&&b.escapeFormulae&&(l=/^[=+\-@\t\r].*$/)}var m=RegExp(q(g),"g");if("string"==typeof a&&(a=JSON.parse(a)),Array.isArray(a)){if(!a.length||Array.isArray(a[0]))return n(null,a,j);else if("object"==typeof a[0])return n(k||Object.keys(a[0]),a,j)}else if("object"==typeof a)return"string"==typeof a.data&&(a.data=JSON.parse(a.data)),Array.isArray(a.data)&&(a.fields||(a.fields=a.meta&&a.meta.fields||k),a.fields||(a.fields=Array.isArray(a.data[0])?a.fields:"object"==typeof a.data[0]?Object.keys(a.data[0]):[]),Array.isArray(a.data[0])||"object"==typeof a.data[0]||(a.data=[a.data])),n(a.fields||[],a.data||[],j);throw Error("Unable to serialize unrecognized input");function n(a,b,c){var g="";"string"==typeof a&&(a=JSON.parse(a)),"string"==typeof b&&(b=JSON.parse(b));var h=Array.isArray(a)&&a.length>0,i=!Array.isArray(b[0]);if(h&&d){for(var j=0;j<a.length;j++)j>0&&(g+=e),g+=o(a[j],j);b.length>0&&(g+=f)}for(var k=0;k<b.length;k++){var l=h?a.length:b[k].length,m=!1,n=h?0===Object.keys(b[k]).length:0===b[k].length;if(c&&!h&&(m="greedy"===c?""===b[k].join("").trim():1===b[k].length&&0===b[k][0].length),"greedy"===c&&h){for(var p=[],q=0;q<l;q++){var r=i?a[q]:q;p.push(b[k][r])}m=""===p.join("").trim()}if(!m){for(var s=0;s<l;s++){s>0&&!n&&(g+=e);var t=h&&i?a[s]:s;g+=o(b[k][t],s)}k<b.length-1&&(!c||l>0&&!n)&&(g+=f)}}return g}function o(a,b){if(null==a)return"";if(a.constructor===Date)return JSON.stringify(a).slice(1,25);var d=!1;l&&"string"==typeof a&&l.test(a)&&(a="'"+a,d=!0);var f=a.toString().replace(m,i);return(d=d||!0===c||"function"==typeof c&&c(a,b)||Array.isArray(c)&&c[b]||function(a,b){for(var c=0;c<b.length;c++)if(a.indexOf(b[c])>-1)return!0;return!1}(f,h.BAD_DELIMITERS)||f.indexOf(e)>-1||" "===f.charAt(0)||" "===f.charAt(f.length-1))?g+f+g:f}},h.RECORD_SEP="\x1e",h.UNIT_SEP="\x1f",h.BYTE_ORDER_MARK="\uFEFF",h.BAD_DELIMITERS=["\r","\n",'"',h.BYTE_ORDER_MARK],h.WORKERS_SUPPORTED=!d&&!!b.Worker,h.NODE_STREAM_INPUT=1,h.LocalChunkSize=0xa00000,h.RemoteChunkSize=5242880,h.DefaultDelimiter=",",h.Parser=r,h.ParserHandle=p,h.NetworkStreamer=k,h.FileStreamer=l,h.StringStreamer=m,h.ReadableStreamStreamer=n,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(h.DuplexStreamStreamer=o),b.jQuery){var i=b.jQuery;i.fn.parse=function(a){var c=a.config||{},d=[];return this.each(function(a){if(!("INPUT"===i(this).prop("tagName").toUpperCase()&&"file"===i(this).attr("type").toLowerCase()&&b.FileReader)||!this.files||0===this.files.length)return!0;for(var e=0;e<this.files.length;e++)d.push({file:this.files[e],inputElem:this,instanceConfig:i.extend({},c)})}),e(),this;function e(){if(0===d.length){x(a.complete)&&a.complete();return}var b=d[0];if(x(a.before)){var c,e,g,j,k=a.before(b.file,b.inputElem);if("object"==typeof k)if("abort"===k.action){return void(c="AbortError",e=b.file,g=b.inputElem,j=k.reason,x(a.error)&&a.error({name:c},e,g,j))}else{if("skip"===k.action)return void f();"object"==typeof k.config&&(b.instanceConfig=i.extend(b.instanceConfig,k.config))}else if("skip"===k)return void f()}var l=b.instanceConfig.complete;b.instanceConfig.complete=function(a){x(l)&&l(a,b.file,b.inputElem),f()},h.parse(b.file,b.instanceConfig)}function f(){d.splice(0,1),e()}}}function j(a){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(a){var b=v(a);b.chunkSize=parseInt(b.chunkSize),a.step||a.chunk||(b.chunkSize=null),this._handle=new p(b),this._handle.streamer=this,this._config=b}).call(this,a),this.parseChunk=function(a,c){let d=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&d>0){let b=this._config.newline;if(!b){let c=this._config.quoteChar||'"';b=this._handle.guessLineEndings(a,c)}a=[...a.split(b).slice(d)].join(b)}if(this.isFirstChunk&&x(this._config.beforeFirstChunk)){var f=this._config.beforeFirstChunk(a);void 0!==f&&(a=f)}this.isFirstChunk=!1,this._halted=!1;var g=this._partialLine+a;this._partialLine="";var i=this._handle.parse(g,this._baseIndex,!this._finished);if(this._handle.paused()||this._handle.aborted()){this._halted=!0;return}var j=i.meta.cursor;this._finished||(this._partialLine=g.substring(j-this._baseIndex),this._baseIndex=j),i&&i.data&&(this._rowCount+=i.data.length);var k=this._finished||this._config.preview&&this._rowCount>=this._config.preview;if(e)b.postMessage({results:i,workerId:h.WORKER_ID,finished:k});else if(x(this._config.chunk)&&!c){if(this._config.chunk(i,this._handle),this._handle.paused()||this._handle.aborted()){this._halted=!0;return}i=void 0,this._completeResults=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(i.data),this._completeResults.errors=this._completeResults.errors.concat(i.errors),this._completeResults.meta=i.meta),!this._completed&&k&&x(this._config.complete)&&(!i||!i.meta.aborted)&&(this._config.complete(this._completeResults,this._input),this._completed=!0),k||i&&i.meta.paused||this._nextChunk(),i},this._sendError=function(a){x(this._config.error)?this._config.error(a):e&&this._config.error&&b.postMessage({workerId:h.WORKER_ID,error:a,finished:!1})}}function k(a){var b;(a=a||{}).chunkSize||(a.chunkSize=h.RemoteChunkSize),j.call(this,a),d?this._nextChunk=function(){this._readChunk(),this._chunkLoaded()}:this._nextChunk=function(){this._readChunk()},this.stream=function(a){this._input=a,this._nextChunk()},this._readChunk=function(){if(this._finished)return void this._chunkLoaded();if(b=new XMLHttpRequest,this._config.withCredentials&&(b.withCredentials=this._config.withCredentials),d||(b.onload=w(this._chunkLoaded,this),b.onerror=w(this._chunkError,this)),b.open(this._config.downloadRequestBody?"POST":"GET",this._input,!d),this._config.downloadRequestHeaders){var a=this._config.downloadRequestHeaders;for(var c in a)b.setRequestHeader(c,a[c])}if(this._config.chunkSize){var e=this._start+this._config.chunkSize-1;b.setRequestHeader("Range","bytes="+this._start+"-"+e)}try{b.send(this._config.downloadRequestBody)}catch(a){this._chunkError(a.message)}d&&0===b.status&&this._chunkError()},this._chunkLoaded=function(){if(4===b.readyState){var a;if(b.status<200||b.status>=400)return void this._chunkError();this._start+=this._config.chunkSize?this._config.chunkSize:b.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null===(a=b.getResponseHeader("Content-Range"))?-1:parseInt(a.substring(a.lastIndexOf("/")+1))),this.parseChunk(b.responseText)}},this._chunkError=function(a){var c=b.statusText||a;this._sendError(Error(c))}}function l(a){(a=a||{}).chunkSize||(a.chunkSize=h.LocalChunkSize),j.call(this,a);var b,c,d="undefined"!=typeof FileReader;this.stream=function(a){this._input=a,c=a.slice||a.webkitSlice||a.mozSlice,d?((b=new FileReader).onload=w(this._chunkLoaded,this),b.onerror=w(this._chunkError,this)):b=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var a=this._input;if(this._config.chunkSize){var e=Math.min(this._start+this._config.chunkSize,this._input.size);a=c.call(a,this._start,e)}var f=b.readAsText(a,this._config.encoding);d||this._chunkLoaded({target:{result:f}})},this._chunkLoaded=function(a){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(a.target.result)},this._chunkError=function(){this._sendError(b.error)}}function m(a){var b;a=a||{},j.call(this,a),this.stream=function(a){return b=a,this._nextChunk()},this._nextChunk=function(){if(!this._finished){var a,c=this._config.chunkSize;return c?(a=b.substring(0,c),b=b.substring(c)):(a=b,b=""),this._finished=!b,this.parseChunk(a)}}}function n(a){a=a||{},j.call(this,a);var b=[],c=!0,d=!1;this.pause=function(){j.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){j.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(a){this._input=a,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){d&&1===b.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),b.length?this.parseChunk(b.shift()):c=!0},this._streamData=w(function(a){try{b.push("string"==typeof a?a:a.toString(this._config.encoding)),c&&(c=!1,this._checkIsFinished(),this.parseChunk(b.shift()))}catch(a){this._streamError(a)}},this),this._streamError=w(function(a){this._streamCleanUp(),this._sendError(a)},this),this._streamEnd=w(function(){this._streamCleanUp(),d=!0,this._streamData("")},this),this._streamCleanUp=w(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function o(a){var b=c(27910).Duplex,d=v(a),e=!0,f=!1,g=[],h=null;this._onCsvData=function(a){var b=a.data;h.push(b)||this._handle.paused()||this._handle.pause()},this._onCsvComplete=function(){h.push(null)},d.step=w(this._onCsvData,this),d.complete=w(this._onCsvComplete,this),j.call(this,d),this._nextChunk=function(){f&&1===g.length&&(this._finished=!0),g.length?g.shift()():e=!0},this._addToParseQueue=function(a,b){g.push(w(function(){if(this.parseChunk("string"==typeof a?a:a.toString(d.encoding)),x(b))return b()},this)),e&&(e=!1,this._nextChunk())},this._onRead=function(){this._handle.paused()&&this._handle.resume()},this._onWrite=function(a,b,c){this._addToParseQueue(a,c)},this._onWriteComplete=function(){f=!0,this._addToParseQueue("")},this.getStream=function(){return h},(h=new b({readableObjectMode:!0,decodeStrings:!1,read:w(this._onRead,this),write:w(this._onWrite,this)})).once("finish",w(this._onWriteComplete,this))}function p(a){var b,c,d,e=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,f=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,g=this,i=0,j=0,k=!1,l=!1,m=[],n={data:[],errors:[],meta:{}};if(x(a.step)){var o=a.step;a.step=function(b){if(n=b,t())s();else{if(s(),0===n.data.length)return;i+=b.data.length,a.preview&&i>a.preview?c.abort():(n.data=n.data[0],o(n,g))}}}function p(b){return"greedy"===a.skipEmptyLines?""===b.join("").trim():1===b.length&&0===b[0].length}function s(){return n&&d&&(u("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+h.DefaultDelimiter+"'"),d=!1),a.skipEmptyLines&&(n.data=n.data.filter(function(a){return!p(a)})),t()&&function(){if(n)if(Array.isArray(n.data[0])){for(var b=0;t()&&b<n.data.length;b++)n.data[b].forEach(c);n.data.splice(0,1)}else n.data.forEach(c);function c(b,c){x(a.transformHeader)&&(b=a.transformHeader(b,c)),m.push(b)}}(),function(){if(!n||!a.header&&!a.dynamicTyping&&!a.transform)return n;function b(b,c){var d,g=a.header?{}:[];for(d=0;d<b.length;d++){var h=d,i=b[d];a.header&&(h=d>=m.length?"__parsed_extra":m[d]),a.transform&&(i=a.transform(i,h)),i=function(b,c){if(a.dynamicTypingFunction&&void 0===a.dynamicTyping[b]&&(a.dynamicTyping[b]=a.dynamicTypingFunction(b)),!0===(a.dynamicTyping[b]||a.dynamicTyping))if("true"===c||"TRUE"===c)return!0;else if("false"===c||"FALSE"===c)return!1;else if(function(a){if(e.test(a)){var b=parseFloat(a);if(b>-0x20000000000000&&b<0x20000000000000)return!0}return!1}(c))return parseFloat(c);else if(f.test(c))return new Date(c);else return""===c?null:c;return c}(h,i),"__parsed_extra"===h?(g[h]=g[h]||[],g[h].push(i)):g[h]=i}return a.header&&(d>m.length?u("FieldMismatch","TooManyFields","Too many fields: expected "+m.length+" fields but parsed "+d,j+c):d<m.length&&u("FieldMismatch","TooFewFields","Too few fields: expected "+m.length+" fields but parsed "+d,j+c)),g}var c=1;return!n.data.length||Array.isArray(n.data[0])?(n.data=n.data.map(b),c=n.data.length):n.data=b(n.data,0),a.header&&n.meta&&(n.meta.fields=m),j+=c,n}()}function t(){return a.header&&0===m.length}function u(a,b,c,d){var e={type:a,code:b,message:c};void 0!==d&&(e.row=d),n.errors.push(e)}this.parse=function(e,f,g){var i=a.quoteChar||'"';if(a.newline||(a.newline=this.guessLineEndings(e,i)),d=!1,a.delimiter)x(a.delimiter)&&(a.delimiter=a.delimiter(e),n.meta.delimiter=a.delimiter);else{var j=function(b,c,d,e,f){var g,i,j,k;f=f||[",","	","|",";",h.RECORD_SEP,h.UNIT_SEP];for(var l=0;l<f.length;l++){var m=f[l],n=0,o=0,q=0;j=void 0;for(var s=new r({comments:e,delimiter:m,newline:c,preview:10}).parse(b),t=0;t<s.data.length;t++){if(d&&p(s.data[t])){q++;continue}var u=s.data[t].length;if(o+=u,void 0===j){j=u;continue}u>0&&(n+=Math.abs(u-j),j=u)}s.data.length>0&&(o/=s.data.length-q),(void 0===i||n<=i)&&(void 0===k||o>k)&&o>1.99&&(i=n,g=m,k=o)}return a.delimiter=g,{successful:!!g,bestDelimiter:g}}(e,a.newline,a.skipEmptyLines,a.comments,a.delimitersToGuess);j.successful?a.delimiter=j.bestDelimiter:(d=!0,a.delimiter=h.DefaultDelimiter),n.meta.delimiter=a.delimiter}var l=v(a);return a.preview&&a.header&&l.preview++,b=e,n=(c=new r(l)).parse(b,f,g),s(),k?{meta:{paused:!0}}:n||{meta:{paused:!1}}},this.paused=function(){return k},this.pause=function(){k=!0,c.abort(),b=x(a.chunk)?"":b.substring(c.getCharIndex())},this.resume=function(){g.streamer._halted?(k=!1,g.streamer.parseChunk(b,!0)):setTimeout(g.resume,3)},this.aborted=function(){return l},this.abort=function(){l=!0,c.abort(),n.meta.aborted=!0,x(a.complete)&&a.complete(n),b=""},this.guessLineEndings=function(a,b){a=a.substring(0,1048576);var c=RegExp(q(b)+"([^]*?)"+q(b),"gm"),d=(a=a.replace(c,"")).split("\r"),e=a.split("\n"),f=e.length>1&&e[0].length<d[0].length;if(1===d.length||f)return"\n";for(var g=0,h=0;h<d.length;h++)"\n"===d[h][0]&&g++;return g>=d.length/2?"\r\n":"\r"}}function q(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function r(a){var b,c=(a=a||{}).delimiter,d=a.newline,e=a.comments,f=a.step,g=a.preview,i=a.fastMode,j=null,k=!1,l=b=void 0===a.quoteChar||null===a.quoteChar?'"':a.quoteChar;if(void 0!==a.escapeChar&&(l=a.escapeChar),("string"!=typeof c||h.BAD_DELIMITERS.indexOf(c)>-1)&&(c=","),e===c)throw Error("Comment character same as delimiter");!0===e?e="#":("string"!=typeof e||h.BAD_DELIMITERS.indexOf(e)>-1)&&(e=!1),"\n"!==d&&"\r"!==d&&"\r\n"!==d&&(d="\n");var m=0,n=!1;this.parse=function(h,o,p){if("string"!=typeof h)throw Error("Input must be a string");var r=h.length,s=c.length,t=d.length,u=e.length,v=x(f);m=0;var w=[],y=[],z=[],A=0;if(!h)return N();if(i||!1!==i&&-1===h.indexOf(b)){for(var B=h.split(d),C=0;C<B.length;C++){if(z=B[C],m+=z.length,C!==B.length-1)m+=d.length;else if(p)break;if(!e||z.substring(0,u)!==e){if(v){if(w=[],J(z.split(c)),O(),n)return N()}else J(z.split(c));if(g&&C>=g)return w=w.slice(0,g),N(!0)}}return N()}for(var D=h.indexOf(c,m),E=h.indexOf(d,m),F=RegExp(q(l)+q(b),"g"),G=h.indexOf(b,m);;){if(h[m]===b){for(G=m,m++;;){if(-1===(G=h.indexOf(b,G+1)))return p||y.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:w.length,index:m}),L();if(G===r-1)return L(h.substring(m,G).replace(F,b));if(b===l&&h[G+1]===l){G++;continue}if(b===l||0===G||h[G-1]!==l){-1!==D&&D<G+1&&(D=h.indexOf(c,G+1)),-1!==E&&E<G+1&&(E=h.indexOf(d,G+1));var H=K(-1===E?D:Math.min(D,E));if(h.substr(G+1+H,s)===c){z.push(h.substring(m,G).replace(F,b)),m=G+1+H+s,h[G+1+H+s]!==b&&(G=h.indexOf(b,m)),D=h.indexOf(c,m),E=h.indexOf(d,m);break}var I=K(E);if(h.substring(G+1+I,G+1+I+t)===d){if(z.push(h.substring(m,G).replace(F,b)),M(G+1+I+t),D=h.indexOf(c,m),G=h.indexOf(b,m),v&&(O(),n))return N();if(g&&w.length>=g)return N(!0);break}y.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:w.length,index:m}),G++;continue}}continue}if(e&&0===z.length&&h.substring(m,m+u)===e){if(-1===E)return N();m=E+t,E=h.indexOf(d,m),D=h.indexOf(c,m);continue}if(-1!==D&&(D<E||-1===E)){z.push(h.substring(m,D)),m=D+s,D=h.indexOf(c,m);continue}if(-1!==E){if(z.push(h.substring(m,E)),M(E+t),v&&(O(),n))return N();if(g&&w.length>=g)return N(!0);continue}break}return L();function J(a){w.push(a),A=m}function K(a){var b=0;if(-1!==a){var c=h.substring(G+1,a);c&&""===c.trim()&&(b=c.length)}return b}function L(a){return p||(void 0===a&&(a=h.substring(m)),z.push(a),m=r,J(z),v&&O()),N()}function M(a){m=a,J(z),z=[],E=h.indexOf(d,m)}function N(b){if(a.header&&!o&&w.length&&!k){let b=w[0],c=Object.create(null),d=new Set(b),e=!1;for(let f=0;f<b.length;f++){let g=b[f];if(x(a.transformHeader)&&(g=a.transformHeader(g,f)),c[g]){let a,h=c[g];do a=`${g}_${h}`,h++;while(d.has(a));d.add(a),b[f]=a,c[g]++,e=!0,null===j&&(j={}),j[a]=g}else c[g]=1,b[f]=g;d.add(g)}e&&console.warn("Duplicate headers found and renamed."),k=!0}return{data:w,errors:y,meta:{delimiter:c,linebreak:d,aborted:n,truncated:!!b,cursor:A+(o||0),renamedHeaders:j}}}function O(){f(N()),w=[],y=[]}},this.abort=function(){n=!0},this.getCharIndex=function(){return m}}function s(a){var b=a.data,c=f[b.workerId],d=!1;if(b.error)c.userError(b.error,b.file);else if(b.results&&b.results.data){var e={abort:function(){d=!0,t(b.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:u,resume:u};if(x(c.userStep)){for(var g=0;g<b.results.data.length&&(c.userStep({data:b.results.data[g],errors:b.results.errors,meta:b.results.meta},e),!d);g++);delete b.results}else x(c.userChunk)&&(c.userChunk(b.results,e,b.file),delete b.results)}b.finished&&!d&&t(b.workerId,b.results)}function t(a,b){var c=f[a];x(c.userComplete)&&c.userComplete(b),c.terminate(),delete f[a]}function u(){throw Error("Not implemented.")}function v(a){if("object"!=typeof a||null===a)return a;var b=Array.isArray(a)?[]:{};for(var c in a)b[c]=v(a[c]);return b}function w(a,b){return function(){a.apply(b,arguments)}}function x(a){return"function"==typeof a}return e&&(b.onmessage=function(a){var c=a.data;if(void 0===h.WORKER_ID&&c&&(h.WORKER_ID=c.workerId),"string"==typeof c.input)b.postMessage({workerId:h.WORKER_ID,results:h.parse(c.input,c.config),finished:!0});else if(b.File&&c.input instanceof File||c.input instanceof Object){var d=h.parse(c.input,c.config);d&&b.postMessage({workerId:h.WORKER_ID,results:d,finished:!0})}}),k.prototype=Object.create(j.prototype),k.prototype.constructor=k,l.prototype=Object.create(j.prototype),l.prototype.constructor=l,m.prototype=Object.create(m.prototype),m.prototype.constructor=m,n.prototype=Object.create(j.prototype),n.prototype.constructor=n,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(o.prototype=Object.create(j.prototype),o.prototype.constructor=o),h})?d.apply(b,[]):d)||(a.exports=e)},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88577:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={fetchUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),getAllUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),fetchLogs:(a,b)=>d.Ay.get(`api/administrator/log/${a}`,{headers:{Authorization:`Bearer ${b}`}}),deleteUser:(a,b)=>d.Ay.delete(`api/administrator/users/${a._id}`,{headers:{Authorization:`Bearer ${b}`}}),fetchUserById:(a,b,c)=>d.Ay.get(`api/administrator/users/${a}`,{headers:{Authorization:`Bearer ${b}`},signal:c}),CreateUser:(a,b)=>d.Ay.post("api/administrator/signup",a,{headers:{Authorization:`Bearer ${b}`}}),updateUser:(a,b)=>d.Ay.put("api/administrator/change-info/",a,{headers:{Authorization:`Bearer ${b}`}}),updatePassUser:(a,b)=>d.Ay.put("api/administrator/users/change-pass/",a,{headers:{Authorization:`Bearer ${b}`}})}},95916:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\components\\\\Form\\\\UploadCSV.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\components\\Form\\UploadCSV.tsx","default")},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isLoading:l}=(0,e.S)(),m=(0,f.useRouter)();if(l)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,3581,1178],()=>b(b.s=71449));module.exports=c})();