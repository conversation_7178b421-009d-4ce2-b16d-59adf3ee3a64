"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { toast } from "react-toastify";
import departmentApiRequest, { Permission, Department } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";
import { ArrowLeft, Save, UserPlus } from "react-feather";

export default function AddMemberPage() {
  const router = useRouter();
  const params = useParams();
  const departmentId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [department, setDepartment] = useState<Department | null>(null);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    phonenumber: "",
    permissions: [] as string[],
  });

  useEffect(() => {
    if (departmentId) {
      fetchDepartmentData();
      fetchAvailablePermissions();
    }
  }, [departmentId]);

  const fetchDepartmentData = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartmentById(departmentId, sessionToken);

      if (result.payload.success) {
        setDepartment(result.payload.department);
        // Set default permissions from department
        setFormData(prev => ({
          ...prev,
          permissions: result.payload.department.defaultPermissions || []
        }));
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error) {
      console.error("Error fetching department:", error);
      toast.error("Lỗi khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchAvailablePermissions = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getAvailablePermissions(sessionToken);

      if (result.payload.success) {
        setAvailablePermissions(result.payload.permissions);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.username.trim() || !formData.email.trim()) {
      toast.error("Vui lòng điền đầy đủ thông tin bắt buộc");
      return;
    }

    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.addMemberToDepartment(
        departmentId,
        formData,
        sessionToken
      );

      if (result.payload.success) {
        toast.success("Thêm thành viên vào phòng ban thành công");
        if (result.payload.generatedPassword) {
          toast.info(`Mật khẩu được tạo tự động: ${result.payload.generatedPassword}`);
        }
        router.push(`/dashboard/departments/${departmentId}`);
      } else {
        toast.error(result.payload.message || "Không thể thêm thành viên");
      }
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Lỗi khi thêm thành viên");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (permissionKey: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permissionKey]
        : prev.permissions.filter(p => p !== permissionKey)
    }));
  };

  // Group permissions by category
  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Không tìm thấy phòng ban</p>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermission="admin">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => router.push(`/dashboard/departments/${departmentId}`)}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Thêm thành viên mới</h1>
            <p className="text-gray-600 mt-1">Thêm thành viên vào phòng ban {department.name}</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <UserPlus size={20} />
              Thông tin cơ bản
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tên người dùng <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập tên người dùng"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập địa chỉ email"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Để trống để tạo tự động"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Nếu để trống, hệ thống sẽ tạo mật khẩu tự động
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  value={formData.phonenumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, phonenumber: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập số điện thoại"
                />
              </div>
            </div>
          </div>

          {/* Permissions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Quyền hạn
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Thành viên sẽ tự động nhận {department.defaultPermissions.length} quyền mặc định của phòng ban.
              Bạn có thể thêm các quyền bổ sung bên dưới.
            </p>

            <div className="space-y-4">
              {Object.entries(groupedPermissions).map(([category, permissions]) => (
                <div key={category} className="border rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">{category}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {permissions.map(permission => (
                      <label key={permission.key} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.permissions.includes(permission.key)}
                          onChange={(e) => handlePermissionChange(permission.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{permission.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-green-800">
                <strong>Tổng cộng:</strong> Thành viên sẽ có {formData.permissions.length} quyền được chọn.
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => router.push(`/dashboard/departments/${departmentId}`)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Save size={16} />
              {loading ? "Đang thêm..." : "Thêm thành viên"}
            </button>
          </div>
        </form>
      </div>
    </PermissionGuard>
  );
}
