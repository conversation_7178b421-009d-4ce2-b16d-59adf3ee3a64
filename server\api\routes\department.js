const express = require('express');
const router = express.Router();
const departmentController = require('../controllers/department');
const passport = require('passport');
const { mongoIdValidation } = require('../middleware/inputValidation');

// Apply authentication middleware to all routes
router.use(passport.authenticate('user', { session: false }));

// Department management routes
// POST /api/departments - Tạo phòng ban mới (chỉ admin)
router.post('/', departmentController.createDepartment);

// POST /api/departments/list - L<PERSON>y danh sách phòng ban với phân trang
router.post('/list', departmentController.getAllDepartments);

// GET /api/departments/permissions - L<PERSON>y danh sách quyền có sẵn
router.get('/permissions', departmentController.getAvailablePermissions);

// GET /api/departments/fix-permissions - S<PERSON>a permission keys cho tất cả phòng ban (utility, no auth)
router.get('/fix-permissions', async (req, res) => {
  try {
    const Department = require("../models/department");
    const departments = await Department.find({ isActive: true });
    let updatedCount = 0;

    for (const dept of departments) {
      if (dept.defaultPermissions && dept.defaultPermissions.length > 0) {
        // Map dept_ prefixed permissions to non-prefixed ones
        const fixedPermissions = dept.defaultPermissions.map(perm => {
          if (perm.startsWith('dept_')) {
            return perm.replace('dept_', '');
          }
          return perm;
        });

        dept.defaultPermissions = fixedPermissions;
        await dept.save();
        updatedCount++;
        console.log(`Fixed permissions for department: ${dept.name}`);
      }
    }

    res.json({
      success: true,
      message: `Đã cập nhật permission keys cho ${updatedCount} phòng ban.`,
      updatedCount
    });
  } catch (error) {
    console.error("Error fixing department permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật permission keys.",
      error: error.message,
    });
  }
});

// POST /api/departments/fix-permissions - Sửa permission keys cho tất cả phòng ban (utility)
router.post('/fix-permissions', departmentController.fixDepartmentPermissions);

// POST /api/departments/update-member-counts - Cập nhật memberCount cho tất cả phòng ban (utility)
router.post('/update-member-counts', departmentController.updateAllMemberCounts);

// GET /api/departments/:id - Lấy thông tin chi tiết phòng ban
router.get('/:id', mongoIdValidation, departmentController.getDepartmentById);

// PUT /api/departments/:id - Cập nhật thông tin phòng ban (chỉ admin)
router.put('/:id', mongoIdValidation, departmentController.updateDepartment);

// DELETE /api/departments/:id - Xóa phòng ban (chỉ admin)
router.delete('/:id', mongoIdValidation, departmentController.deleteDepartment);

// Department member management routes
// POST /api/departments/:id/members - Thêm thành viên vào phòng ban
router.post('/:id/members', mongoIdValidation, departmentController.addMemberToDepartment);

// POST /api/departments/:id/members/list - Lấy danh sách thành viên phòng ban
router.post('/:id/members/list', mongoIdValidation, departmentController.getDepartmentMembers);

// PUT /api/departments/:id/members/:memberId - Cập nhật quyền thành viên
router.put('/:id/members/:memberId',
  mongoIdValidation,
  departmentController.updateMemberPermissions
);

// DELETE /api/departments/:id/members/:memberId - Xóa thành viên khỏi phòng ban
router.delete('/:id/members/:memberId',
  mongoIdValidation,
  departmentController.removeMemberFromDepartment
);

module.exports = router;
