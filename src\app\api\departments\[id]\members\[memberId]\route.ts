import { NextRequest, NextResponse } from 'next/server';

interface Params {
  id: string;
  memberId: string;
}

// PUT /api/departments/[id]/members/[memberId] - Update member permissions
export async function PUT(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_ENDPOINT || 'http://localhost:3000';
    const body = await request.json();
    const authorization = request.headers.get('authorization');
    
    console.log(`🔍 Forwarding update member request for department ${params.id}, member ${params.memberId}`);
    
    const response = await fetch(`${baseUrl}/api/departments/${params.id}/members/${params.memberId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
      body: JSON.stringify(body),
    });
    
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error forwarding update member request:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/departments/[id]/members/[memberId] - Remove member from department
export async function DELETE(
  request: NextRequest,
  { params }: { params: Params }
) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_ENDPOINT || 'http://localhost:3000';
    const authorization = request.headers.get('authorization');
    
    console.log(`🔍 Forwarding remove member request for department ${params.id}, member ${params.memberId}`);
    
    const response = await fetch(`${baseUrl}/api/departments/${params.id}/members/${params.memberId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });
    
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error forwarding remove member request:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
