(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4476],{9424:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(12115),a=t(38637),n=t.n(a);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}var i=(0,s.forwardRef)(function(e,r){var t=e.color,a=e.size,n=void 0===a?24:a,i=function(e,r){if(null==e)return{};var t,s,a=function(e,r){if(null==e)return{};var t,s,a={},n=Object.keys(e);for(s=0;s<n.length;s++)t=n[s],r.indexOf(t)>=0||(a[t]=e[t]);return a}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)t=n[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return s.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:n,height:n,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),s.createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:n().string,size:n().oneOfType([n().string,n().number])},i.displayName="ArrowLeft";let l=i},11080:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(12115),a=t(38637),n=t.n(a);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}var i=(0,s.forwardRef)(function(e,r){var t=e.color,a=e.size,n=void 0===a?24:a,i=function(e,r){if(null==e)return{};var t,s,a=function(e,r){if(null==e)return{};var t,s,a={},n=Object.keys(e);for(s=0;s<n.length;s++)t=n[s],r.indexOf(t)>=0||(a[t]=e[t]);return a}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)t=n[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return s.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:n,height:n,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),s.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),s.createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:n().string,size:n().oneOfType([n().string,n().number])},i.displayName="Save";let l=i},11725:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(27937);let a={fetchUsers:(e,r)=>s.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),getAllUsers:(e,r)=>s.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),fetchLogs:(e,r)=>s.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),deleteUser:(e,r)=>s.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(r)}}),fetchUserById:(e,r,t)=>s.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(r)},signal:t}),CreateUser:(e,r)=>s.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(r)}}),updateUser:(e,r)=>s.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(r)}}),updatePassUser:(e,r)=>s.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(r)}})}},88771:(e,r,t)=>{Promise.resolve().then(t.bind(t,91902))},91902:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(95155),a=t(12115),n=t(35695),o=t(38543),i=t(3136),l=t(11725),c=t(87708),d=t(9424),u=t(11080);function g(){let e=(0,n.useRouter)(),[r,t]=(0,a.useState)(!1),[g,p]=(0,a.useState)([]),[h,m]=(0,a.useState)([]),[y,b]=(0,a.useState)({name:"",description:"",defaultPermissions:[],managerId:""});(0,a.useEffect)(()=>{f(),x()},[]);let f=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await i.A.getAvailablePermissions(e);r.payload.success&&p(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},x=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await l.A.getAllUsers({page:1,perPage:100},e);if(r.payload.success){let e=r.payload.users.filter(e=>!e.department||"department_manager"!==e.rule);m(e)}}catch(e){console.error("Error fetching users:",e)}},v=async r=>{if(r.preventDefault(),!y.name.trim())return void o.oR.error("Vui l\xf2ng nhập t\xean ph\xf2ng ban");try{t(!0);let r=localStorage.getItem("sessionToken")||"",s=await i.A.createDepartment(y,r);s.payload.success?(o.oR.success("Tạo ph\xf2ng ban th\xe0nh c\xf4ng"),e.push("/dashboard/departments")):o.oR.error(s.payload.message||"Kh\xf4ng thể tạo ph\xf2ng ban")}catch(e){console.error("Error creating department:",e),o.oR.error("Lỗi khi tạo ph\xf2ng ban")}finally{t(!1)}},j=g.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return(0,s.jsx)(c.default,{requiredPermission:"admin",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(d.A,{size:20})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Th\xeam ph\xf2ng ban mới"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo ph\xf2ng ban mới với quyền mặc định"})]})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin cơ bản"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean ph\xf2ng ban ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:y.name,onChange:e=>b(r=>({...r,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean ph\xf2ng ban",required:!0})]})}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,s.jsx)("textarea",{value:y.description,onChange:e=>b(r=>({...r,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về ph\xf2ng ban"})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),(0,s.jsxs)("select",{value:y.managerId,onChange:e=>b(r=>({...r,managerId:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Chọn quản l\xfd ph\xf2ng ban"}),h.map(e=>(0,s.jsxs)("option",{value:e._id,children:[e.username," (",e.email,")"]},e._id))]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền mặc định cho người d\xf9ng"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c quyền n\xe0y sẽ được tự động g\xe1n cho tất cả người d\xf9ng mới của ph\xf2ng ban"}),(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(j).map(e=>{let[r,t]=e;return(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:r}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:t.map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:y.defaultPermissions.includes(e.key),onChange:r=>{var t,s;return t=e.key,s=r.target.checked,void b(e=>({...e,defaultPermissions:s?[...e.defaultPermissions,t]:e.defaultPermissions.filter(e=>e!==t)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},r)})})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e.back(),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsxs)("button",{type:"submit",disabled:r,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,s.jsx)(u.A,{size:16}),r?"Đang tạo...":"Tạo ph\xf2ng ban"]})]})]})]})})}}},e=>{e.O(0,[9268,3235,8543,7617,8441,5964,7358],()=>e(e.s=88771)),_N_E=e.O()}]);