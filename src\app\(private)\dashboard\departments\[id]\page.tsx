"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { toast } from "react-toastify";
import departmentApiRequest, { Department, DepartmentMember } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";
import { ArrowLeft, Edit, Users, Plus, Settings, RefreshCw } from "react-feather";
import { Badge, RoleBadge } from "@/components/ui/Badge";
import { getPermissionDisplayName } from "@/utils/permissions";

export default function DepartmentDetailPage() {
  const router = useRouter();
  const params = useParams();
  const departmentId = params.id as string;
  
  const [department, setDepartment] = useState<Department | null>(null);
  const [members, setMembers] = useState<DepartmentMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [membersLoading, setMembersLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (departmentId) {
      fetchDepartmentDetail();
      fetchMembers();
    }
  }, [departmentId, refreshKey]);

  // Function to refresh all data
  const refreshData = () => {
    setRefreshKey(prev => prev + 1);
    fetchDepartmentDetail();
    fetchMembers();
  };

  // Refresh data when component becomes visible (user returns from other pages)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && departmentId) {
        refreshData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [departmentId]);

  const fetchDepartmentDetail = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartmentById(departmentId, sessionToken);
      
      if (result.payload.success) {
        setDepartment(result.payload.department);
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error) {
      console.error("Error fetching department:", error);
      toast.error("Lỗi khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setLoading(false);
    }
  };

  const fetchMembers = async () => {
    try {
      setMembersLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartmentMembers(
        departmentId,
        { page: 1, perPage: 50 },
        sessionToken
      );
      
      if (result.payload.success) {
        setMembers(result.payload.members);
      }
    } catch (error) {
      console.error("Error fetching members:", error);
    } finally {
      setMembersLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Không tìm thấy phòng ban</p>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermission="admin">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push("/dashboard/departments")}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{department.name}</h1>
              {department.description && (
                <p className="text-gray-600 mt-1">{department.description}</p>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={refreshData}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              title="Làm mới dữ liệu"
            >
              <RefreshCw size={16} />
            </button>
            <button
              onClick={() => router.push(`/dashboard/departments/${departmentId}/edit`)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit size={16} />
              Chỉnh sửa
            </button>
          </div>
        </div>

        {/* Department Info */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin phòng ban</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tên phòng ban
              </label>
              <p className="text-gray-900">{department.name}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quản lý phòng ban
              </label>
              {department.manager ? (
                <div>
                  <p className="font-medium text-gray-900">{department.manager.username}</p>
                  <p className="text-sm text-gray-500">{department.manager.email}</p>
                </div>
              ) : (
                <p className="text-gray-400 italic">Chưa có quản lý</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số thành viên
              </label>
              <div className="flex items-center gap-2">
                <Users size={16} className="text-gray-500" />
                <span className="font-medium text-gray-900">{members.length}</span>
              </div>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mô tả
              </label>
              <p className="text-gray-900">{department.description || "Không có mô tả"}</p>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quyền mặc định ({department.defaultPermissions.length} quyền)
              </label>
              <div className="flex flex-wrap gap-2">
                {department.defaultPermissions.length > 0 ? (
                  department.defaultPermissions.map(permission => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {getPermissionDisplayName(permission)}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-400 italic">Không có quyền mặc định</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <Badge variant={department.isActive ? "success" : "danger"}>
                {department.isActive ? "Hoạt động" : "Không hoạt động"}
              </Badge>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ngày tạo
              </label>
              <p className="text-gray-900">
                {new Date(department.createdAt).toLocaleDateString("vi-VN")}
              </p>
            </div>
          </div>
        </div>

        {/* Members List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Danh sách thành viên ({members.length})
              </h2>
              <button
                onClick={() => router.push(`/dashboard/departments/${departmentId}/members/add`)}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus size={16} />
                Thêm thành viên
              </button>
            </div>
          </div>

          <div className="p-6">
            {membersLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : members.length > 0 ? (
              <div className="space-y-4">
                {members.map(member => (
                  <div key={member._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div>
                        <h3 className="font-medium text-gray-900">{member.username}</h3>
                        <p className="text-sm text-gray-500">{member.email}</p>
                        {member.phonenumber && (
                          <p className="text-sm text-gray-500">{member.phonenumber}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <RoleBadge role={member.rule as "admin" | "manager" | "user" | "editor"} />
                        {member.departmentRole && member.departmentRole !== 'member' && (
                          <div className="mt-1">
                            <Badge variant="info" className="text-xs">
                              {member.departmentRole === 'manager' ? 'Quản lý' : member.departmentRole}
                            </Badge>
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          {member.permissions.length} quyền
                        </p>
                      </div>
                      
                      <button
                        onClick={() => router.push(`/dashboard/departments/${departmentId}/members/${member._id}/edit`)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Chỉnh sửa quyền"
                      >
                        <Settings size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Chưa có thành viên nào trong phòng ban
              </div>
            )}
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
