(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4375],{2478:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(95155),s=t(63560),l=t(62177),o=t(75937),n=t(12115),c=t(20174),i=t(48819),d=t(83931),u=t(38543),g=t(43888),h=t(58890);let m=()=>{var e;let[r,t]=(0,n.useState)(!1),[m,p]=(0,n.useState)({}),[x,f]=(0,n.useState)(!1),y=(0,l.mN)({resolver:(0,s.u)(i.ri),defaultValues:{ads1:"",logo:{path:"",folder:"",_id:""}}}),{reset:b}=y;(0,n.useEffect)(()=>{(async()=>{try{t(!0);let e=localStorage.getItem("sessionToken")||"",r=await d.A.fetchSetting(e);r.payload.success?(p(r.payload.setting),b(r.payload.setting)):console.error("Failed to fetch")}catch(e){console.error("Error fetching:",e)}finally{t(!1)}})()},[b]);let j=async e=>{try{let r=localStorage.getItem("sessionToken")||"",t=await d.A.EditorSetting(e,r);t.payload.success?(u.oR.success("Th\xe0nh C\xf4ng"),p(t.payload.setting)):(u.oR.error("An error occurred during update. Please try again."),console.error("Error creating category:",t.payload.message))}catch(e){console.error("Unexpected error:",e),u.oR.error("An error occurred during update. Please try again.")}},w=async e=>{if(!r){t(!0);try{let r=new FormData;r.append("imageFile",e);let t=localStorage.getItem("sessionToken")||"",a=await h.A.postLogo(r,t);a&&(u.oR.success("Đăng h\xecnh ảnh th\xe0nh c\xf4ng."),setTimeout(()=>{y.setValue("logo",a.payload.featureImg)},3e3))}catch(e){u.oR.error("An error occurred during update your profile. Please try again.")}finally{t(!1)}}};return(0,a.jsx)("div",{children:(0,a.jsx)(o.lV,{...y,children:(0,a.jsxs)("form",{onSubmit:y.handleSubmit(j),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,a.jsx)(o.zB,{control:y.control,name:"ads1",render:e=>{let{field:r}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{children:"Khung quảng c\xe1o 1"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)("textarea",{...r,placeholder:"M\xe3 Quảng c\xe1o",className:"border p-2 rounded w-full h-32"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"block mb-2",children:"Logo website"}),(0,a.jsx)(g.A,{serverImageUrl:null==m||null==(e=m.logo)?void 0:e.path,onUploadFeatureImg:w,onDeleteFeatureImg:()=>{y.setValue("logo",{path:"",folder:"",_id:""})}})]})]}),(0,a.jsx)("button",{disabled:r,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:r?(0,a.jsx)(c.A,{className:"animate-spin"}):"Submit"})]})})})};function p(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h1",{className:"text-2xl mb-4",children:"C\xe0i đặt"}),(0,a.jsx)(m,{})]})}},64192:(e,r,t)=>{Promise.resolve().then(t.bind(t,2478))}},e=>{e.O(0,[9268,3235,8543,2182,4612,8441,5964,7358],()=>e(e.s=64192)),_N_E=e.O()}]);