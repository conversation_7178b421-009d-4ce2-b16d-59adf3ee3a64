"use client";
import { useState } from "react";
import { Shield, Check, X, Edit3, Save, XCircle } from "react-feather";

interface UserPermissionsProps {
  user: {
    _id: string;
    username: string;
    email: string;
    rule: string;
    permissions?: string[];
  };
  onPermissionsUpdate?: (userId: string, permissions: string[]) => void;
}

// All available permissions with descriptions
const AVAILABLE_PERMISSIONS = [
  {
    id: 'user_view',
    name: '<PERSON>em danh sách người dùng',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể xem danh sách và thông tin người dùng'
  },
  {
    id: 'user_add',
    name: 'Thêm người dùng mới',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể tạo tài khoản người dùng mới'
  },
  {
    id: 'user_edit',
    name: 'Chỉnh sửa người dùng',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể chỉnh sửa thông tin người dùng'
  },
  {
    id: 'user_delete',
    name: '<PERSON><PERSON><PERSON> người dùng',
    category: 'Quản lý người dùng',
    description: 'Có thể xóa tài khoản người dùng'
  },
  {
    id: 'user_import_csv',
    name: 'Nhập thành viên từ CSV',
    category: 'Quản lý thành viên',
    description: 'Có thể nhập danh sách thành viên từ file CSV'
  },
  {
    id: 'file_view',
    name: 'Xem danh sách file',
    category: 'Quản lý file',
    description: 'Có thể xem danh sách file và tài liệu'
  },
  {
    id: 'file_upload',
    name: 'Tải lên file',
    category: 'Quản lý file',
    description: 'Có thể tải lên file và tài liệu mới'
  },
  {
    id: 'file_delete',
    name: 'Xóa file',
    category: 'Quản lý file',
    description: 'Có thể xóa file và tài liệu'
  },
  
  // Court Case Management permissions
  {
    id: 'court_case_view',
    name: 'Xem danh sách vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem danh sách và thông tin vụ việc tòa án'
  },
  {
    id: 'court_case_create',
    name: 'Tạo vụ việc mới',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể tạo vụ việc tòa án mới'
  },
  {
    id: 'court_case_edit',
    name: 'Chỉnh sửa vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể chỉnh sửa thông tin vụ việc tòa án'
  },
  {
    id: 'court_case_delete',
    name: 'Xóa vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xóa vụ việc tòa án'
  },
  {
    id: 'court_case_export',
    name: 'Xuất dữ liệu vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xuất danh sách vụ việc ra file Excel/CSV'
  },
  {
    id: 'court_case_import',
    name: 'Nhập dữ liệu vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể nhập danh sách vụ việc từ file Excel'
  },
  {
    id: 'court_case_stats_view',
    name: 'Xem thống kê vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem thống kê cơ bản về vụ việc tòa án'
  },
  {
    id: 'court_case_detailed_stats_view',
    name: 'Xem thống kê chi tiết',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem thống kê chi tiết và báo cáo phân tích'
  },

  // Court Case User Account Management permissions
  {
    id: 'court_case_user_profile_view',
    name: 'Xem hồ sơ người dùng',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể xem thông tin hồ sơ người dùng trong hệ thống vụ việc'
  },
  {
    id: 'court_case_user_profile_edit',
    name: 'Chỉnh sửa hồ sơ người dùng',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể chỉnh sửa thông tin hồ sơ người dùng'
  },
  {
    id: 'court_case_user_password_change',
    name: 'Đổi mật khẩu người dùng',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể thay đổi mật khẩu cho người dùng'
  },
  {
    id: 'court_case_user_permissions_view',
    name: 'Xem quyền hạn người dùng',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể xem danh sách quyền hạn của người dùng'
  },
  {
    id: 'court_case_user_permissions_edit',
    name: 'Chỉnh sửa quyền hạn',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể cấp và thu hồi quyền hạn cho người dùng'
  },
  {
    id: 'court_case_user_activity_log_view',
    name: 'Xem nhật ký hoạt động',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể xem lịch sử hoạt động của người dùng'
  },
  {
    id: 'court_case_user_two_factor_manage',
    name: 'Quản lý xác thực 2 yếu tố',
    category: 'Quản lý tài khoản trong vụ việc',
    description: 'Có thể bật/tắt và quản lý xác thực 2 yếu tố'
  },

  {
    id: 'system_settings_view',
    name: 'Xem cài đặt hệ thống',
    category: 'Cài đặt hệ thống',
    description: 'Có thể xem các cài đặt hệ thống'
  },
  {
    id: 'system_settings_edit',
    name: 'Chỉnh sửa cài đặt hệ thống',
    category: 'Cài đặt hệ thống',
    description: 'Có thể thay đổi cài đặt hệ thống'
  },
  {
    id: 'analytics_view',
    name: 'Xem thống kê',
    category: 'Thống kê',
    description: 'Có thể xem báo cáo và thống kê hệ thống'
  },
  {
    id: 'permissions_manage',
    name: 'Quản lý quyền',
    category: 'Quản lý quyền',
    description: 'Có thể cấp và thu hồi quyền của thành viên'
  }
];

export default function UserPermissions({ user, onPermissionsUpdate }: UserPermissionsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingPermissions, setEditingPermissions] = useState<string[]>(user.permissions || []);

  const userPermissions = user.permissions || [];
  const isAdmin = user.rule === 'admin';

  // Group permissions by category
  const groupedPermissions = AVAILABLE_PERMISSIONS.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof AVAILABLE_PERMISSIONS>);

  const handlePermissionToggle = (permissionId: string) => {
    if (editingPermissions.includes(permissionId)) {
      setEditingPermissions(editingPermissions.filter(p => p !== permissionId));
    } else {
      setEditingPermissions([...editingPermissions, permissionId]);
    }
  };

  const handleSave = () => {
    if (onPermissionsUpdate) {
      onPermissionsUpdate(user._id, editingPermissions);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditingPermissions(user.permissions || []);
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="text-blue-500" size={24} />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Quyền truy cập</h3>
            <p className="text-sm text-gray-500">
              {isAdmin ? 'Quản trị viên có tất cả quyền' : `${userPermissions.length} quyền được cấp`}
            </p>
          </div>
        </div>
        
        {!isAdmin && (
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm"
                >
                  <Save size={16} />
                  <span>Lưu</span>
                </button>
                <button
                  onClick={handleCancel}
                  className="flex items-center space-x-1 px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm"
                >
                  <XCircle size={16} />
                  <span>Hủy</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
              >
                <Edit3 size={16} />
                <span>Chỉnh sửa</span>
              </button>
            )}
          </div>
        )}
      </div>

      {isAdmin ? (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Shield className="text-blue-600" size={20} />
            <span className="text-blue-800 font-medium">
              Quản trị viên có quyền truy cập tất cả các tính năng
            </span>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedPermissions).map(([category, permissions]) => (
            <div key={category} className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
              <div className="space-y-2">
                {permissions.map((permission) => {
                  const hasPermission = isEditing 
                    ? editingPermissions.includes(permission.id)
                    : userPermissions.includes(permission.id);
                  
                  return (
                    <div key={permission.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {isEditing ? (
                          <button
                            onClick={() => handlePermissionToggle(permission.id)}
                            className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                              hasPermission
                                ? 'bg-green-500 border-green-500 text-white'
                                : 'border-gray-300 hover:border-green-400'
                            }`}
                          >
                            {hasPermission && <Check size={12} />}
                          </button>
                        ) : (
                          <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                            hasPermission
                              ? 'bg-green-500 border-green-500 text-white'
                              : 'bg-red-100 border-red-300 text-red-500'
                          }`}>
                            {hasPermission ? <Check size={12} /> : <X size={12} />}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className={`font-medium ${
                            hasPermission ? 'text-gray-900' : 'text-gray-500'
                          }`}>
                            {permission.name}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            hasPermission 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            {hasPermission ? 'Có quyền' : 'Không có quyền'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          {permission.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
