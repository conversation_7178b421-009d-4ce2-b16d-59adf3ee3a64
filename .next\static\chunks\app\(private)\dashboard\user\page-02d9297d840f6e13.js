(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4778],{11725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(27937);let s={fetchUsers:(e,t)=>a.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>a.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>a.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>a.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>a.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>a.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>a.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>a.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},18579:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(95155);function s(e){let{currentPage:t,totalPages:r,onPageChange:s}=e;return(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,a.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>s(Math.max(t-1,1)),disabled:1===t,children:"Previous"}),(0,a.jsxs)("span",{children:["Page ",t," / ",r]}),(0,a.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>s(Math.min(t+1,r)),disabled:t===r,children:"Next"})]})}r(12115)},18617:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(27937);let s={getDashboardStats:e=>a.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}}),getRecentActivities:(e,t)=>a.Ay.get("/api/administrator/recent-activities".concat(t?"?limit=".concat(t):""),{headers:{Authorization:"Bearer ".concat(e)}}),toggleUserPrivate:(e,t)=>a.Ay.put("/api/administrator/update-private",{id:e},{headers:{Authorization:"Bearer ".concat(t)}})}},23348:(e,t,r)=>{"use strict";r.d(t,{U:()=>i,default:()=>l});var a=r(95155),s=r(12115);let n=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,s.useContext)(n),l=e=>{let{children:t}=e,[r,i]=(0,s.useState)(()=>null),[l,o]=(0,s.useState)(!0),c=(0,s.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),o(!1)},[i]),(0,a.jsx)(n.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:l},children:t})}},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var a=r(84559),s=r(59434),n=r(35695);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class l extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let o=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,h=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),g=await fetch(h,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),m=null,p=g.headers.get("content-type");if(p&&p.includes("application/json"))try{m=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),m=null}else m=await g.text();let y={status:g.status,payload:m};if(!g.ok)if(404===g.status||403===g.status)throw new l(y);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!o){o=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await o}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),o=null,location.href="/login"}}}else throw new i(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=m;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},38497:(e,t,r)=>{"use strict";r.d(t,{S:()=>s});var a=r(23348);let s=()=>{let{user:e,isLoading:t}=(0,a.U)();return{hasPermission:r=>{var a;return!t&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isLoading:t}}},52814:(e,t,r)=>{"use strict";r.d(t,{Ex:()=>s,eG:()=>n});var a=r(95155);r(12115);let s=e=>{let{children:t,variant:r="default",size:s="md",className:n="",dot:i=!1}=e;return(0,a.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[r],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[s],"\n        ").concat(n,"\n      "),children:[i&&(0,a.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[r])}),t]})},n=e=>{let{role:t,className:r=""}=e,n={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}}[t];return(0,a.jsx)(s,{variant:n.variant,className:r,children:n.label})}},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>i,cn:()=>n}),r(27937);var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}r(58801);let i=e=>e.startsWith("/")?e.slice(1):e},67234:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var a=r(95155),s=r(12115),n=r(36268),i=r(11032),l=r(11725),o=r(18617),c=r(38543),d=r(35695),u=r(18579),h=r(52814);function g(){let[e,t]=(0,s.useState)([]),[r,g]=(0,s.useState)(1),[m,p]=(0,s.useState)(1),[y,x]=(0,s.useState)(!1),b=async()=>{x(!0);try{let e=localStorage.getItem("sessionToken")||"",a=await l.A.fetchUsers({page:r,perPage:20},e);if(console.log("Fetch users response:",a),a&&a.payload){let{total:e,users:r}=a.payload;t(r||[]),p(Math.ceil(e/20))}else console.warn("No payload in users response"),t([])}catch(e){console.error("Error fetching users:",e),t([]),c.oR.error("An error occurred while fetching users. Please try again.")}finally{x(!1)}};(0,s.useEffect)(()=>{b()},[r]);let f=async(r,a)=>{try{let s=localStorage.getItem("sessionToken")||"",n=await l.A.deleteUser(r,s);if(n.payload.success){let r=[...e];r.splice(a,1),t(r),c.oR.success("Delete successful!")}else console.error("Error deleting user:",n.payload),c.oR.error("Error deleting user. Please try again.")}catch(e){console.error("Unexpected error:",e),c.oR.error("An error occurred during deletion. Please try again.")}},v=async e=>{let t=e.private?"mở kho\xe1":"kho\xe1";if(confirm("Bạn c\xf3 chắc chắn muốn ".concat(t," t\xe0i khoản ").concat(e.username,"?")))try{let r=localStorage.getItem("sessionToken")||"";(await o.A.toggleUserPrivate(e._id,r)).payload.success?(c.oR.success("".concat(t.charAt(0).toUpperCase()+t.slice(1)," t\xe0i khoản th\xe0nh c\xf4ng")),b()):c.oR.error("Kh\xf4ng thể ".concat(t," t\xe0i khoản"))}catch(e){console.error("Error toggling user private status:",e),c.oR.error("C\xf3 lỗi xảy ra khi ".concat(t," t\xe0i khoản"))}},N=[{accessorKey:"username",header:"T\xean"},{accessorKey:"phonenumber",header:"Số điện thoại"},{accessorKey:"email",header:"Email"},{accessorKey:"department",header:"Ph\xf2ng ban",cell:e=>{let{row:t}=e;return(0,a.jsx)("div",{children:t.original.department?(0,a.jsx)("div",{className:"font-medium text-sm",children:t.original.department.name}):(0,a.jsx)("span",{className:"text-gray-400 italic text-sm",children:"Chưa c\xf3 ph\xf2ng ban"})})}},{accessorKey:"rule",header:"Chức vụ",cell:e=>{let{row:t}=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)(h.eG,{role:t.original.rule}),t.original.departmentRole&&"member"!==t.original.departmentRole&&(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:"manager"===t.original.departmentRole?"Quản l\xfd":t.original.departmentRole})})]})}},{accessorKey:"private",header:"T\xecnh Trạng",cell:e=>{let{row:t}=e;return(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(t.original.private?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:t.original.private?"Đ\xe3 kho\xe1":"Hoạt động"})}},{accessorKey:"createdAt",header:"Ng\xe0y đăng",cell:e=>{let{row:t}=e;return new Date(t.original.createdAt).toLocaleDateString()}},{header:"H\xe0nh động",cell:e=>{let{row:t,rowIndex:r}=e,s=(0,d.useRouter)();return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>{s.push("/dashboard/user/".concat(t.original._id))},className:"px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Chỉnh sửa"}),(0,a.jsx)("button",{onClick:()=>v(t.original),className:"px-3 py-1 text-sm rounded-md ".concat(t.original.private?"bg-green-500 hover:bg-green-600 text-white":"bg-yellow-500 hover:bg-yellow-600 text-white"),children:t.original.private?"Mở kho\xe1":"Kho\xe1"}),(0,a.jsx)("button",{onClick:()=>f(t.original,t.index),className:"px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600",children:"X\xf3a"})]})}}],j=(0,n.N4)({data:e||[],columns:N,getCoreRowModel:(0,i.HT)()});return y?(0,a.jsx)("div",{className:"w-full p-4 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-600",children:"Đang tải dữ liệu..."})}):(0,a.jsx)("div",{className:"w-full p-4",children:0===e.length?(0,a.jsx)("div",{className:"text-center text-gray-600 py-8",children:"Kh\xf4ng c\xf3 dữ liệu người d\xf9ng"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("table",{className:"table-auto w-full border-collapse border border-gray-300 bg-white",children:[(0,a.jsx)("thead",{children:j.getHeaderGroups().map(e=>(0,a.jsx)("tr",{className:"bg-gray-200",children:e.headers.map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-gray-800 font-semibold",children:(0,n.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)("tbody",{children:j.getRowModel().rows.map(e=>(0,a.jsx)("tr",{className:"border border-gray-300 hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-2 text-gray-900",children:(0,n.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),(0,a.jsx)(u.A,{currentPage:r,totalPages:m,onPageChange:e=>g(e)})]})})}},79970:(e,t,r)=>{Promise.resolve().then(r.bind(r,87708)),Promise.resolve().then(r.bind(r,67234))},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(74556),s=r(49509);let n=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=n.data},87708:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(95155),s=r(38497),n=r(35695),i=r(12115);function l(e){let{children:t,requiredPermission:r,requiredPermissions:l=[],requireAll:o=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:h,isLoading:g}=(0,s.S)(),m=(0,n.useRouter)();if((0,i.useEffect)(()=>{if(!g&&!h)(r?d(r):!(l.length>0)||(o?l.every(e=>d(e)):u(l)))||m.replace(c)},[d,u,h,g,r,l,o,c,m]),g)return(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,a.jsx)(a.Fragment,{children:t});return(r?d(r):!(l.length>0)||(o?l.every(e=>d(e)):u(l)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,a.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}}},e=>{e.O(0,[9268,3235,8543,6268,8441,5964,7358],()=>e(e.s=79970)),_N_E=e.O()}]);