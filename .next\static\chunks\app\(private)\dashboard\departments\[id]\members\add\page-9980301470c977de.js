(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1234],{5122:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var n=t(95155),s=t(12115),a=t(35695),o=t(38543),l=t(3136),i=t(87708),c=t(9424),d=t(38637),u=t.n(d);function h(){return(h=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var m=(0,s.forwardRef)(function(e,r){var t=e.color,n=e.size,a=void 0===n?24:n,o=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)t=a[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return s.createElement("svg",h({ref:r,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),s.createElement("path",{d:"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),s.createElement("circle",{cx:"8.5",cy:"7",r:"4"}),s.createElement("line",{x1:"20",y1:"8",x2:"20",y2:"14"}),s.createElement("line",{x1:"23",y1:"11",x2:"17",y2:"11"}))});m.propTypes={color:u().string,size:u().oneOfType([u().string,u().number])},m.displayName="UserPlus";var p=t(11080);function g(){let e=(0,a.useRouter)(),r=(0,a.useParams)().id,[t,d]=(0,s.useState)(!1),[u,h]=(0,s.useState)(!0),[g,y]=(0,s.useState)([]),[x,b]=(0,s.useState)(null),[f,v]=(0,s.useState)({username:"",email:"",password:"",phonenumber:"",permissions:[]});(0,s.useEffect)(()=>{r&&(j(),w())},[r]);let j=async()=>{try{let t=localStorage.getItem("sessionToken")||"",n=await l.A.getDepartmentById(r,t);n.payload.success?(b(n.payload.department),v(e=>({...e,permissions:n.payload.department.defaultPermissions||[]}))):(o.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments"))}catch(r){console.error("Error fetching department:",r),o.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}finally{h(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await l.A.getAvailablePermissions(e);r.payload.success&&y(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},N=async t=>{if(t.preventDefault(),!f.username.trim()||!f.email.trim())return void o.oR.error("Vui l\xf2ng điền đầy đủ th\xf4ng tin bắt buộc");try{d(!0);let t=localStorage.getItem("sessionToken")||"",n=await l.A.addMemberToDepartment(r,f,t);n.payload.success?(o.oR.success("Th\xeam th\xe0nh vi\xean v\xe0o ph\xf2ng ban th\xe0nh c\xf4ng"),n.payload.generatedPassword&&o.oR.info("Mật khẩu được tạo tự động: ".concat(n.payload.generatedPassword)),e.push("/dashboard/departments/".concat(r))):o.oR.error(n.payload.message||"Kh\xf4ng thể th\xeam th\xe0nh vi\xean")}catch(e){console.error("Error adding member:",e),o.oR.error("Lỗi khi th\xeam th\xe0nh vi\xean")}finally{d(!1)}},k=g.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return u?(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):x?(0,n.jsx)(i.default,{requiredPermission:"admin",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,n.jsx)("button",{onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(c.A,{size:20})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Th\xeam th\xe0nh vi\xean mới"}),(0,n.jsxs)("p",{className:"text-gray-600 mt-1",children:["Th\xeam th\xe0nh vi\xean v\xe0o ph\xf2ng ban ",x.name]})]})]}),(0,n.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,n.jsx)(m,{size:20}),"Th\xf4ng tin cơ bản"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean người d\xf9ng ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:f.username,onChange:e=>v(r=>({...r,username:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean người d\xf9ng",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Email ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"email",value:f.email,onChange:e=>v(r=>({...r,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập địa chỉ email",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mật khẩu"}),(0,n.jsx)("input",{type:"password",value:f.password,onChange:e=>v(r=>({...r,password:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Để trống để tạo tự động"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Nếu để trống, hệ thống sẽ tạo mật khẩu tự động"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),(0,n.jsx)("input",{type:"tel",value:f.phonenumber,onChange:e=>v(r=>({...r,phonenumber:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập số điện thoại"})]})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền hạn"}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Th\xe0nh vi\xean sẽ tự động nhận ",x.defaultPermissions.length," quyền mặc định của ph\xf2ng ban. Bạn c\xf3 thể th\xeam c\xe1c quyền bổ sung b\xean dưới."]}),(0,n.jsx)("div",{className:"space-y-4",children:Object.entries(k).map(e=>{let[r,t]=e;return(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:r}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:t.map(e=>(0,n.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:f.permissions.includes(e.key),onChange:r=>{var t,n;return t=e.key,n=r.target.checked,void v(e=>({...e,permissions:n?[...e.permissions,t]:e.permissions.filter(e=>e!==t)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},r)})}),(0,n.jsx)("div",{className:"mt-4 p-3 bg-green-50 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-green-800",children:[(0,n.jsx)("strong",{children:"Tổng cộng:"})," Th\xe0nh vi\xean sẽ c\xf3 ",f.permissions.length," quyền được chọn."]})})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)("button",{type:"button",onClick:()=>e.push("/dashboard/departments/".concat(r)),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsxs)("button",{type:"submit",disabled:t,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,n.jsx)(p.A,{size:16}),t?"Đang th\xeam...":"Th\xeam th\xe0nh vi\xean"]})]})]})]})}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy ph\xf2ng ban"})})}},9424:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(12115),s=t(38637),a=t.n(s);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,r){var t=e.color,s=e.size,a=void 0===s?24:s,l=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)t=a[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return n.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),n.createElement("polyline",{points:"12 19 5 12 12 5"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="ArrowLeft";let i=l},11080:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(12115),s=t(38637),a=t.n(s);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,r){var t=e.color,s=e.size,a=void 0===s?24:s,l=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)t=a[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return n.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),n.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),n.createElement("polyline",{points:"7 3 7 8 15 8"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Save";let i=l},61761:(e,r,t)=>{Promise.resolve().then(t.bind(t,5122))}},e=>{e.O(0,[9268,3235,8543,7617,8441,5964,7358],()=>e(e.s=61761)),_N_E=e.O()}]);