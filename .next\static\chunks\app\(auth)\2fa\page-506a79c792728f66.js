(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2766],{24311:(e,a,t)=>{Promise.resolve().then(t.bind(t,45664))},45664:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var n=t(95155),c=t(12115),s=t(35695),l=t(74660),i=t(71592);function r(){return(0,n.jsx)(c.Suspense,{fallback:(0,n.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,n.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:"Loading..."})}),children:(0,n.jsx)(d,{})})}function d(){let e=(0,s.useSearchParams)().get("id")||"",[a,t]=(0,c.useState)(""),[r,d]=(0,c.useState)("");return((0,c.useEffect)(()=>{if(!e)return void d("Invalid verification code.");(async()=>{try{var a,n;let c=await i.A.checkCode(e),s=null!=(n=null==c||null==(a=c.payload)?void 0:a.userId)?n:"";t(s)}catch(e){d("Failed to load verification. Please try again later.")}})()},[e]),r)?(0,n.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,n.jsx)("p",{children:r})}):a?(0,n.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,n.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,n.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,n.jsx)("h1",{className:"text-2xl text-center mb-4",children:"X\xe1c nhận bước 2"}),(0,n.jsx)("span",{className:"text-center block mb-4",children:"Mở ứng dụng x\xe1c thực hai yếu tố tr\xean thiết bị của bạn để xem m\xe3 x\xe1c thực v\xe0 x\xe1c minh danh t\xednh của bạn"}),(0,n.jsx)(l.A,{userId:a,typeVerify:"authapp"})]})})}):(0,n.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,n.jsx)("p",{children:"Loading user information..."})})}}},e=>{e.O(0,[9268,3235,8543,2182,716,3085,8441,5964,7358],()=>e(e.s=24311)),_N_E=e.O()}]);