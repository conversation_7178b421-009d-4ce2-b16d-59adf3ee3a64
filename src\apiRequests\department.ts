import http from "@/lib/http";

export interface Department {
  _id: string;
  name: string;
  description: string;
  manager?: {
    _id: string;
    username: string;
    email: string;
  };
  defaultPermissions: string[];
  isActive: boolean;
  memberCount: number;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface DepartmentMember {
  _id: string;
  username: string;
  email: string;
  phonenumber?: string;
  rule: string;
  departmentRole: string;
  permissions: string[];
  createdAt: string;
}

export interface Permission {
  key: string;
  name: string;
  category: string;
}

export interface CreateDepartmentBody {
  name: string;
  description?: string;
  defaultPermissions?: string[];
  managerId?: string;
}

export interface UpdateDepartmentBody {
  name?: string;
  description?: string;
  defaultPermissions?: string[];
  managerId?: string;
}

export interface AddMemberBody {
  username: string;
  email: string;
  password?: string;
  phonenumber?: string;
  permissions?: string[];
}

export interface UpdateMemberPermissionsBody {
  permissions?: string[];
  departmentRole?: string;
}

const departmentApiRequest = {
  // Tạo phòng ban mới
  createDepartment: (body: CreateDepartmentBody, sessionToken: string) =>
    http.post<{ success: boolean; message: string; department: Department }>(
      "/api/departments",
      body,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Lấy danh sách phòng ban
  getDepartments: (
    body: { page?: number; perPage?: number; search?: string },
    sessionToken: string
  ) =>
    http.post<{
      success: boolean;
      departments: Department[];
      total: number;
      page: number;
      perPage: number;
    }>("/api/departments/list", body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Lấy thông tin chi tiết phòng ban
  getDepartmentById: (id: string, sessionToken: string) =>
    http.get<{ success: boolean; department: Department }>(
      `/api/departments/${id}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Cập nhật thông tin phòng ban
  updateDepartment: (
    id: string,
    body: UpdateDepartmentBody,
    sessionToken: string
  ) =>
    http.put<{ success: boolean; message: string; department: Department }>(
      `/api/departments/${id}`,
      body,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Xóa phòng ban
  deleteDepartment: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(
      `/api/departments/${id}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Thêm thành viên vào phòng ban
  addMemberToDepartment: (
    departmentId: string,
    body: AddMemberBody,
    sessionToken: string
  ) =>
    http.post<{
      success: boolean;
      message: string;
      user: DepartmentMember;
      generatedPassword?: string;
    }>(`/api/departments/${departmentId}/members`, body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Lấy danh sách thành viên phòng ban
  getDepartmentMembers: (
    departmentId: string,
    body: { page?: number; perPage?: number; search?: string },
    sessionToken: string
  ) =>
    http.post<{
      success: boolean;
      members: DepartmentMember[];
      total: number;
      page: number;
      perPage: number;
    }>(`/api/departments/${departmentId}/members/list`, body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Cập nhật quyền thành viên
  updateMemberPermissions: (
    departmentId: string,
    memberId: string,
    body: UpdateMemberPermissionsBody,
    sessionToken: string
  ) =>
    http.put<{
      success: boolean;
      message: string;
      member: DepartmentMember;
    }>(`/api/departments/${departmentId}/members/${memberId}`, body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Xóa thành viên khỏi phòng ban
  removeMemberFromDepartment: (
    departmentId: string,
    memberId: string,
    sessionToken: string
  ) =>
    http.delete<{ success: boolean; message: string }>(
      `/api/departments/${departmentId}/members/${memberId}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Lấy danh sách quyền có sẵn
  getAvailablePermissions: (sessionToken: string) =>
    http.get<{ success: boolean; permissions: Permission[] }>(
      "/api/departments/permissions",
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),
};

export default departmentApiRequest;
