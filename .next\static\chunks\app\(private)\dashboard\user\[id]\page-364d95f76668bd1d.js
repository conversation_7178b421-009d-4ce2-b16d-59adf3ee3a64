(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3664],{11725:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var n=t(27937);let s={fetchUsers:(e,r)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),getAllUsers:(e,r)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),fetchLogs:(e,r)=>n.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),deleteUser:(e,r)=>n.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(r)}}),fetchUserById:(e,r,t)=>n.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(r)},signal:t}),CreateUser:(e,r)=>n.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(r)}}),updateUser:(e,r)=>n.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(r)}}),updatePassUser:(e,r)=>n.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(r)}})}},20174:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115),s=t(38637),i=t.n(s);function a(){return(a=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var o=(0,n.forwardRef)(function(e,r){var t=e.color,s=e.size,i=void 0===s?24:s,o=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},i=Object.keys(e);for(n=0;n<i.length;n++)t=i[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),n.createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),n.createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),n.createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),n.createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),n.createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),n.createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),n.createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),n.createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});o.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},o.displayName="Loader";let l=o},23348:(e,r,t)=>{"use strict";t.d(r,{U:()=>a,default:()=>o});var n=t(95155),s=t(12115);let i=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),a=()=>(0,s.useContext)(i),o=e=>{let{children:r}=e,[t,a]=(0,s.useState)(()=>null),[o,l]=(0,s.useState)(!0),c=(0,s.useCallback)(e=>{a(e),localStorage.setItem("user",JSON.stringify(e))},[a]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");a(e?JSON.parse(e):null),l(!1)},[a]),(0,n.jsx)(i.Provider,{value:{user:t,setUser:c,isAuthenticated:!!t,isLoading:o},children:r})}},27937:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>d});var n=t(84559),s=t(59434),i=t(35695);class a extends Error{constructor({status:e,payload:r}){super("Http Error"),this.status=e,this.payload=r}}class o extends a{constructor({status:e,payload:r}){super({status:e,payload:r}),this.status=e,this.payload=r}}let l=null,c=async(e,r,t)=>{let c;(null==t?void 0:t.body)instanceof FormData?c=t.body:(null==t?void 0:t.body)&&(c=JSON.stringify(t.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==t?void 0:t.baseUrl)===void 0?n.A.NEXT_PUBLIC_API_ENDPOINT:t.baseUrl,h=r.startsWith("/")?"".concat(u).concat(r):"".concat(u,"/").concat(r),m=await fetch(h,{...t,headers:{...d,...null==t?void 0:t.headers},body:c,method:e}),p=null,g=m.headers.get("content-type");if(g&&g.includes("application/json"))try{p=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await m.text();let x={status:m.status,payload:p};if(!m.ok)if(404===m.status||403===m.status)throw new o(x);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,i.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new a(x);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(r))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(r)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return x},d={get:(e,r)=>c("GET",e,r),post:(e,r,t)=>c("POST",e,{...t,body:r}),put:(e,r,t)=>c("PUT",e,{...t,body:r}),patch:(e,r,t)=>c("PATCH",e,{...t,body:r}),delete:(e,r)=>c("DELETE",e,{...r})}},38497:(e,r,t)=>{"use strict";t.d(r,{S:()=>s});var n=t(23348);let s=()=>{let{user:e,isLoading:r}=(0,n.U)();return{hasPermission:t=>{var n;return!r&&!!e&&("admin"===e.rule||(null==(n=e.permissions)?void 0:n.includes(t))||!1)},hasAnyPermission:t=>!r&&!!e&&("admin"===e.rule||t.some(r=>{var t;return null==(t=e.permissions)?void 0:t.includes(r)})),getAllPermissions:()=>r||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!r&&(null==e?void 0:e.rule)==="admin",isLoading:r}}},38637:(e,r,t)=>{e.exports=t(79399)()},59434:(e,r,t)=>{"use strict";t.d(r,{Fd:()=>a,cn:()=>i}),t(27937);var n=t(52596),s=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}t(58801);let a=e=>e.startsWith("/")?e.slice(1):e},59698:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115),s=t(38637),i=t.n(s);function a(){return(a=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var o=(0,n.forwardRef)(function(e,r){var t=e.color,s=e.size,i=void 0===s?24:s,o=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},i=Object.keys(e);for(n=0;n<i.length;n++)t=i[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),n.createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),n.createElement("circle",{cx:"12",cy:"12",r:"3"}))});o.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},o.displayName="Eye";let l=o},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var n=t(95155),s=t(59434),i=t(99310),a=t(59698),o=t(12115);let l=o.forwardRef((e,r)=>{let{className:t,type:l,...c}=e,[d,u]=(0,o.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"relative w-full",children:[(0,n.jsx)("input",{type:"password"===l&&d?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,s.cn)("input input-bordered w-full rounded-md",t),ref:r,...c}),"password"===l&&(d?(0,n.jsx)(i.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}):(0,n.jsx)(a.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}))]})})});l.displayName="Input"},71951:(e,r,t)=>{Promise.resolve().then(t.bind(t,85520))},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},75937:(e,r,t)=>{"use strict";t.d(r,{lV:()=>u,MJ:()=>v,zB:()=>m,eI:()=>x,lR:()=>f,C5:()=>y});var n=t(95155),s=t(12115),i=t(54624),a=t(62177),o=t(59434),l=t(87073);let c=(0,t(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(l.b,{ref:r,className:(0,o.cn)(c(),t),...s})});d.displayName=l.b.displayName;let u=a.Op,h=s.createContext({}),m=e=>{let{...r}=e;return(0,n.jsx)(h.Provider,{value:{name:r.name},children:(0,n.jsx)(a.xI,{...r})})},p=()=>{let e=s.useContext(h),r=s.useContext(g),{getFieldState:t,formState:n}=(0,a.xW)(),i=t(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},g=s.createContext({}),x=s.forwardRef((e,r)=>{let{className:t,...i}=e,a=s.useId();return(0,n.jsx)(g.Provider,{value:{id:a},children:(0,n.jsx)("div",{ref:r,className:(0,o.cn)("mb-4",t),...i})})});x.displayName="FormItem";let f=s.forwardRef((e,r)=>{let{className:t,...s}=e,{error:i,formItemId:a}=p();return(0,n.jsx)(d,{ref:r,className:(0,o.cn)(i&&"text-destructive",t),htmlFor:a,...s})});f.displayName="FormLabel";let v=s.forwardRef((e,r)=>{let{...t}=e,{error:s,formItemId:a,formDescriptionId:o,formMessageId:l}=p();return(0,n.jsx)(i.DX,{ref:r,id:a,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...t})});v.displayName="FormControl",s.forwardRef((e,r)=>{let{className:t,...s}=e,{formDescriptionId:i}=p();return(0,n.jsx)("p",{ref:r,id:i,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",t),...s})}).displayName="FormDescription";let y=s.forwardRef((e,r)=>{let{className:t,children:s,...i}=e,{error:a,formMessageId:l}=p(),c=a?String(null==a?void 0:a.message):s;return c?(0,n.jsx)("p",{ref:r,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-red-600",t),...i,children:c}):null});y.displayName="FormMessage"},79399:(e,r,t)=>{"use strict";var n=t(72948);function s(){}function i(){}i.resetWarningCache=s,e.exports=function(){function e(e,r,t,s,i,a){if(a!==n){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function r(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,elementType:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:i,resetWarningCache:s};return t.PropTypes=t,t}},84559:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(74556),s=t(49509);let i=n.Ik({NEXT_PUBLIC_API_ENDPOINT:n.Yj().url(),NEXT_PUBLIC_URL:n.Yj().url(),CRYPTOJS_SECRECT:n.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!i.success)throw console.error("Invalid environment variables:",i.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let a=i.data},84649:(e,r,t)=>{"use strict";t.d(r,{PD:()=>i,aP:()=>a,gS:()=>o});var n=t(74556);let s=n.Ik({_id:n.Yj(),username:n.Yj(),phonenumber:n.ai(),email:n.Yj().email(),createdAt:n.Yj().date(),private:n.zM(),rule:n.Yj()});n.Ik({total:n.ai(),users:n.YO(s)});let i=n.Ik({username:n.Yj().trim().min(2).max(256),email:n.Yj().email(),password:n.Yj().min(6).max(100),phonenumber:n.Yj(),department:n.Yj().optional(),permissions:n.YO(n.Yj()).optional()}).strict(),a=n.Ik({_id:n.Yj(),username:n.Yj().trim().min(2).max(256),email:n.Yj().email(),phonenumber:n.bz(),private:n.zM(),rule:n.k5(["user","admin","manager","editor"]),rank:n.Yj(),gender:n.k5(["Male","Female","Not"]),bio:n.bz(),permissions:n.YO(n.Yj())}),o=n.Ik({_id:n.Yj(),password:n.Yj().min(6,"Mật khẩu phải c\xf3 \xedt nhất 6 k\xfd tự"),confirmPassword:n.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Mật khẩu x\xe1c nhận kh\xf4ng khớp",path:["confirmPassword"]}),l=n.Ik({_id:n.Yj(),user:n.Yj(),ip:n.Yj(),device:n.Yj(),loginTime:n.Yj().datetime(),logoutTime:n.Yj().datetime().nullable()});n.Ik({logs:n.YO(l)})},85520:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var n=t(95155),s=t(12115),i=t(75937),a=t(62523),o=t(20174),l=t(62177),c=t(84649),d=t(63560),u=t(35695);let h=e=>{let{user:r,onSubmit:t,onSubmitPass:h}=e,[m,p]=(0,s.useState)(!1);(0,u.useRouter)();let[g,x]=(0,s.useState)(null),f=["Male","Female","Not"],v=["1","2","3","4","5"],y=["user","manager","editor"],j=(0,l.mN)({resolver:(0,d.u)(c.aP),defaultValues:r||{_id:"",email:"",username:"",phonenumber:"",private:!1,rule:"user",rank:"1",gender:"Not",bio:"",permissions:[]}}),b=j.watch("rule");s.useEffect(()=>{r&&(console.log("Resetting form with user data:",r),j.reset(r))},[r,j]),s.useEffect(()=>{console.log("Form state:",{isValid:j.formState.isValid,errors:j.formState.errors,values:j.getValues()}),Object.keys(j.formState.errors).length>0&&(console.log("Detailed validation errors:"),Object.entries(j.formState.errors).forEach(e=>{let[r,t]=e;console.log('Field "'.concat(r,'":'),t)}))},[j.formState.isValid,j.formState.errors]);let w=(0,l.mN)({resolver:(0,d.u)(c.gS),defaultValues:{_id:(null==r?void 0:r._id)||"",password:"",confirmPassword:""}});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.lV,{...j,children:(0,n.jsxs)("form",{onSubmit:j.handleSubmit(e=>{var r;console.log("Form submitted with data:",e),console.log("Form errors:",j.formState.errors);let n={...e,phonenumber:(null==(r=e.phonenumber)?void 0:r.toString())||"",bio:e.bio||"",permissions:Array.isArray(e.permissions)?e.permissions:[]};console.log("Transformed data:",n),t(n)},e=>{console.log("Form validation errors:",e)}),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,n.jsx)(i.zB,{control:j.control,name:"username",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"User Name"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)(a.p,{placeholder:"username",...r})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"email",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Email"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)(a.p,{placeholder:"email",type:"email",...r})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"phonenumber",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Số điện thoại"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)(a.p,{placeholder:"Số điện thoại",type:"text",...r})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"gender",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Giới T\xednh"}),(0,n.jsx)(i.MJ,{children:(0,n.jsxs)("select",{...r,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,n.jsx)("option",{value:"",children:"Chọn giới t\xednh"}),f.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"rule",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Chức Vụ"}),(0,n.jsx)(i.MJ,{children:(0,n.jsxs)("select",{...r,className:"w-full p-2 border border-gray-300 rounded-md",disabled:"admin"===b,children:[(0,n.jsx)("option",{value:"",children:"Chọn chức vụ"}),y.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"rank",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Cấp độ th\xe0nh vi\xean"}),(0,n.jsx)(i.MJ,{children:(0,n.jsxs)("select",{...r,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,n.jsx)("option",{value:"",children:"Chọn cấp độ"}),v.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"bio",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Tiểu sử"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)("textarea",{...r,rows:3,className:"w-full p-2 border border-gray-300 rounded-md"})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"private",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Kho\xe1 Th\xe0nh vi\xean"}),(0,n.jsx)(i.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-col space-y-3 mt-2",children:[(0,n.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)("input",{type:"radio",name:"private-".concat(r.name),className:"w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3",value:"true",checked:!0===r.value,onChange:()=>r.onChange(!0)}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Kho\xe1 Kh\xe1ch h\xe0ng"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản sẽ bị v\xf4 hiệu h\xf3a"})]})]}),(0,n.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)("input",{type:"radio",name:"private-".concat(r.name),className:"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3",value:"false",checked:!1===r.value,onChange:()=>r.onChange(!1)}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("span",{className:"font-medium text-gray-900",children:"Hoạt động"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản hoạt động b\xecnh thường"})]})]})]})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:j.control,name:"permissions",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{className:"col-span-2",children:[(0,n.jsx)(i.lR,{children:"Ph\xe2n quyền chức năng chi tiết"}),(0,n.jsx)("div",{className:"space-y-6 mt-4",children:[{title:"Th\xe0nh Vi\xean",permissions:[{id:"user_view",name:"Quản L\xfd Th\xe0nh Vi\xean",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin th\xe0nh vi\xean"},{id:"user_add",name:"Th\xeam Th\xe0nh Vi\xean",description:"Tạo t\xe0i khoản th\xe0nh vi\xean mới"},{id:"user_edit",name:"Chỉnh Sửa Th\xe0nh Vi\xean",description:"Cập nhật th\xf4ng tin th\xe0nh vi\xean"},{id:"user_delete",name:"X\xf3a Th\xe0nh Vi\xean",description:"X\xf3a t\xe0i khoản th\xe0nh vi\xean"},{id:"user_import_csv",name:"Nhập File CSV",description:"Import th\xe0nh vi\xean từ file CSV"}]},{title:"Quản L\xfd File",permissions:[{id:"file_view",name:"Xem File",description:"Xem danh s\xe1ch file v\xe0 t\xe0i liệu"},{id:"file_upload",name:"Upload File",description:"Tải l\xean file v\xe0 t\xe0i liệu mới"},{id:"file_delete",name:"X\xf3a File",description:"X\xf3a file v\xe0 t\xe0i liệu"}]},{title:"Quản L\xfd Vụ Việc T\xf2a \xc1n",permissions:[{id:"court_case_view",name:"Xem Danh S\xe1ch Vụ Việc",description:"Xem danh s\xe1ch v\xe0 th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_create",name:"Tạo Vụ Việc Mới",description:"Tạo vụ việc t\xf2a \xe1n mới"},{id:"court_case_edit",name:"Chỉnh Sửa Vụ Việc",description:"Chỉnh sửa th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_delete",name:"X\xf3a Vụ Việc",description:"X\xf3a vụ việc t\xf2a \xe1n"},{id:"court_case_export",name:"Xuất Dữ Liệu Vụ Việc",description:"Xuất danh s\xe1ch vụ việc ra file Excel/CSV"},{id:"court_case_import",name:"Nhập Dữ Liệu Vụ Việc",description:"Nhập danh s\xe1ch vụ việc từ file Excel"},{id:"court_case_stats_view",name:"Xem Thống K\xea Vụ Việc",description:"Xem thống k\xea cơ bản về vụ việc t\xf2a \xe1n"},{id:"court_case_detailed_stats_view",name:"Xem Thống K\xea Chi Tiết",description:"Xem thống k\xea chi tiết v\xe0 b\xe1o c\xe1o ph\xe2n t\xedch"}]},{title:"Quản L\xfd T\xe0i Khoản Trong Vụ Việc",permissions:[{id:"court_case_user_profile_view",name:"Xem Hồ Sơ Người D\xf9ng",description:"Xem th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"court_case_user_profile_edit",name:"Chỉnh Sửa Hồ Sơ",description:"Chỉnh sửa th\xf4ng tin hồ sơ người d\xf9ng"},{id:"court_case_user_password_change",name:"Đổi Mật Khẩu Người D\xf9ng",description:"Thay đổi mật khẩu cho người d\xf9ng"},{id:"court_case_user_permissions_view",name:"Xem Quyền Hạn",description:"Xem danh s\xe1ch quyền hạn của người d\xf9ng"},{id:"court_case_user_permissions_edit",name:"Chỉnh Sửa Quyền Hạn",description:"Cấp v\xe0 thu hồi quyền hạn cho người d\xf9ng"},{id:"court_case_user_activity_log_view",name:"Xem Nhật K\xfd Hoạt Động",description:"Xem lịch sử hoạt động của người d\xf9ng"},{id:"court_case_user_two_factor_manage",name:"Quản L\xfd X\xe1c Thực 2 Yếu Tố",description:"Bật/tắt v\xe0 quản l\xfd x\xe1c thực 2 yếu tố"}]},{title:"C\xe0i Đặt Hệ Thống",permissions:[{id:"system_settings_view",name:"Xem C\xe0i Đặt",description:"Xem cấu h\xecnh hệ thống"},{id:"system_settings_edit",name:"Chỉnh Sửa C\xe0i Đặt",description:"Thay đổi cấu h\xecnh hệ thống"}]},{title:"Thống K\xea & Ph\xe2n Quyền",permissions:[{id:"analytics_view",name:"Xem Thống K\xea",description:"Truy cập b\xe1o c\xe1o v\xe0 thống k\xea"},{id:"permissions_manage",name:"Quản L\xfd Ph\xe2n Quyền",description:"Cấp v\xe0 thu hồi quyền cho người d\xf9ng"}]}].map(e=>(0,n.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,n.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,n.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),e.title]}),(0,n.jsx)("div",{className:"space-y-2",children:e.permissions.map(e=>{var t;return(0,n.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors",children:[(0,n.jsx)("input",{type:"checkbox",className:"mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",value:e.id,checked:null==(t=r.value)?void 0:t.includes(e.id),onChange:t=>{var n;let s=t.target.checked?[...r.value||[],e.id]:(null==(n=r.value)?void 0:n.filter(r=>r!==e.id))||[];r.onChange(s)}}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,n.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.id)})})]},e.title))}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400",children:[(0,n.jsx)("strong",{children:"Lưu \xfd:"})," Admin lu\xf4n c\xf3 tất cả quyền. Chỉ cần cấp quyền cụ thể cho Manager v\xe0 User."]})]})}})]}),(0,n.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:g}),(0,n.jsx)("div",{className:"flex gap-4 justify-center mt-6",children:(0,n.jsxs)("button",{disabled:!!m,type:"submit",onClick:()=>{console.log("Submit button clicked"),console.log("Loading state:",m),console.log("Form valid:",j.formState.isValid),console.log("Form errors:",j.formState.errors)},className:"btn btn-primary bg-blue-700 w-40 text-white flex items-center",children:[m?(0,n.jsx)(o.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})})]})}),(0,n.jsx)(i.lV,{...w,children:(0,n.jsxs)("form",{onSubmit:w.handleSubmit(h),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8",noValidate:!0,children:[(0,n.jsx)(i.zB,{control:w.control,name:"password",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"Mật khẩu mới"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)(a.p,{placeholder:"password",type:"password",...r})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsx)(i.zB,{control:w.control,name:"confirmPassword",render:e=>{let{field:r}=e;return(0,n.jsxs)(i.eI,{children:[(0,n.jsx)(i.lR,{children:"X\xe1c nhận mật khẩu"}),(0,n.jsx)(i.MJ,{children:(0,n.jsx)(a.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...r})}),(0,n.jsx)(i.C5,{})]})}}),(0,n.jsxs)("button",{disabled:!!m,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[m?(0,n.jsx)(o.A,{className:"animate-spin"}):"","Update Password"]})]})})]})};var m=t(11725),p=t(38543),g=t(6874),x=t.n(g),f=t(87708);function v(e){let{params:r}=e,[t,i]=(0,s.useState)(null),a=(0,s.use)(r).id;(0,s.useEffect)(()=>{let e=new AbortController,{signal:r}=e,t=async()=>{try{let t=localStorage.getItem("sessionToken")||"";console.log("Fetching user with ID:",a),console.log("Session token exists:",!!t);let n=await m.A.fetchUserById(a,t,r);if(!r.aborted)if(console.log("User fetch result:",n),n.payload.success)i(n.payload.user);else{var e;console.error("Error fetching user:",n.payload),p.oR.error("Failed to fetch user data: "+((null==(e=n.payload)?void 0:e.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){r.aborted||(console.error("Unexpected error:",e),p.oR.error("An error occurred while fetching user data"))}};return a&&t(),()=>{e.abort()}},[a]);let o=async e=>{try{console.log("Submitting user update data:",e);let t=localStorage.getItem("sessionToken")||"",n=await m.A.updateUser(e,t);if(console.log("Update result:",n),n.payload.success)i(n.payload.user),p.oR.success("Cập nhật th\xe0nh c\xf4ng!");else{var r;console.error("Error updating user:",n.payload),p.oR.error("Kh\xf4ng thể cập nhật: "+((null==(r=n.payload)?void 0:r.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),p.oR.error("C\xf3 lỗi xảy ra khi cập nhật. Vui l\xf2ng thử lại.")}},l=async e=>{try{console.log("Submitting password change data:",e);let t=localStorage.getItem("sessionToken")||"",n=await m.A.updatePassUser(e,t);if(console.log("Password change result:",n),n.payload.success)p.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng!");else{var r;console.error("Error changing password:",n.payload),p.oR.error("Kh\xf4ng thể đổi mật khẩu: "+((null==(r=n.payload)?void 0:r.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),p.oR.error("C\xf3 lỗi xảy ra khi đổi mật khẩu. Vui l\xf2ng thử lại.")}};return(0,n.jsx)(f.default,{requiredPermission:"user_edit",children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa t\xe0i khoản"}),(0,n.jsx)(x(),{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",href:"/dashboard/user/log/".concat(null==t?void 0:t._id),children:"Xem User log"})]}),t?(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,n.jsx)(h,{onSubmit:o,onSubmitPass:l,user:t})]})}):(0,n.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-gray-500",children:"Đang tải th\xf4ng tin..."})]})})]})})}},87073:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var n=t(12115);t(47650);var s=t(54624),i=t(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,s.TL)(`Primitive.${r}`),a=n.forwardRef((e,n)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?t:r,{...a,ref:n})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{}),o=n.forwardRef((e,r)=>(0,i.jsx)(a.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var l=o},87708:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var n=t(95155),s=t(38497),i=t(35695),a=t(12115);function o(e){let{children:r,requiredPermission:t,requiredPermissions:o=[],requireAll:l=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:h,isLoading:m}=(0,s.S)(),p=(0,i.useRouter)();if((0,a.useEffect)(()=>{if(!m&&!h)(t?d(t):!(o.length>0)||(l?o.every(e=>d(e)):u(o)))||p.replace(c)},[d,u,h,m,t,o,l,c,p]),m)return(0,n.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,n.jsx)(n.Fragment,{children:r});return(t?d(t):!(o.length>0)||(l?o.every(e=>d(e)):u(o)))?(0,n.jsx)(n.Fragment,{children:r}):(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,n.jsx)("button",{onClick:()=>p.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},99310:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115),s=t(38637),i=t.n(s);function a(){return(a=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var o=(0,n.forwardRef)(function(e,r){var t=e.color,s=e.size,i=void 0===s?24:s,o=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t,n,s={},i=Object.keys(e);for(n=0;n<i.length;n++)t=i[n],r.indexOf(t)>=0||(s[t]=e[t]);return s}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:r,xmlns:"http://www.w3.org/2000/svg",width:i,height:i,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),n.createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),n.createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});o.propTypes={color:i().string,size:i().oneOfType([i().string,i().number])},o.displayName="EyeOff";let l=o}},e=>{e.O(0,[9268,3235,8543,2182,6874,8441,5964,7358],()=>e(e.s=71951)),_N_E=e.O()}]);