const Department = require("../models/department");
const User = require("../models/user");
const { customAlphabet } = require("nanoid");
const bcrypt = require("bcryptjs");
const { ForbiddenError } = require("@casl/ability");
const { defineAbilityFor } = require("../permissions/abilities");

// Tạo phòng ban mới
exports.createDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    // Chỉ admin mới có thể tạo phòng ban
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể tạo phòng ban mới.",
      });
    }

    const { name, description, defaultPermissions, managerId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban là bắt buộc.",
      });
    }

    // Kiểm tra tên phòng ban đã tồn tại
    const existingDept = await Department.findOne({ name });

    if (existingDept) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban đã tồn tại.",
      });
    }

    // Kiểm tra manager nếu có
    let manager = null;
    if (managerId) {
      manager = await User.findById(managerId);
      if (!manager) {
        return res.status(400).json({
          success: false,
          message: "Không tìm thấy người quản lý được chỉ định.",
        });
      }
    }

    const department = new Department({
      name,
      description,
      defaultPermissions: defaultPermissions || [],
      manager: managerId || null,
      createdBy: req.user._id,
    });

    ForbiddenError.from(ability).throwUnlessCan("create", department);
    await department.save();

    // Cập nhật role cho manager nếu có
    if (manager) {
      manager.department = department._id;
      manager.rule = 'department_manager';
      manager.departmentRole = 'manager';
      await manager.save();
    }

    await department.populate('manager', 'username email');
    await department.populate('createdBy', 'username email');

    res.status(201).json({
      success: true,
      message: "Tạo phòng ban thành công.",
      department,
    });
  } catch (error) {
    console.error("Error creating department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi tạo phòng ban.",
      error: error.message,
    });
  }
};

// Lấy danh sách tất cả phòng ban
exports.getAllDepartments = async (req, res) => {

  try {
    const { page = 1, perPage = 20, search = "" } = req.body;

    let filter = { isActive: true };

    // Nếu không phải admin, chỉ xem được phòng ban của mình
    if (req.user.rule !== 'admin') {
      if (req.user.department) {
        filter._id = req.user.department;
      } else {
        return res.json({
          success: true,
          departments: [],
          total: 0,
        });
      }
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * perPage;

    const [departments, total] = await Promise.all([
      Department.find(filter)
        .populate('manager', 'username email')
        .populate('createdBy', 'username email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(perPage),
      Department.countDocuments(filter)
    ]);

    // Calculate real-time member count for each department
    const departmentsWithMemberCount = await Promise.all(
      departments.map(async (dept) => {
        const memberCount = await User.countDocuments({ department: dept._id });
        return {
          ...dept.toObject(),
          memberCount
        };
      })
    );

    res.json({
      success: true,
      departments: departmentsWithMemberCount,
      total,
      page,
      perPage,
    });
  } catch (error) {
    console.error("Error getting departments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách phòng ban.",
      error: error.message,
    });
  }
};

// Lấy thông tin chi tiết phòng ban
exports.getDepartmentById = async (req, res) => {

  try {
    const { id } = req.params;

    const department = await Department.findById(id)
      .populate('manager', 'username email rule departmentRole')
      .populate('createdBy', 'username email')
      .populate({
        path: 'members',
        select: 'username email rule departmentRole createdAt',
        match: { isActive: { $ne: false } }
      });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Kiểm tra quyền truy cập
    if (req.user.rule !== 'admin' &&
        (!req.user.department || req.user.department.toString() !== id)) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xem thông tin phòng ban này.",
      });
    }

    res.json({
      success: true,
      department,
    });
  } catch (error) {
    console.error("Error getting department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy thông tin phòng ban.",
      error: error.message,
    });
  }
};

// Cập nhật thông tin phòng ban
exports.updateDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;
    const { name, description, defaultPermissions, managerId } = req.body;

    // Chỉ admin mới có thể cập nhật phòng ban
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể cập nhật thông tin phòng ban.",
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Kiểm tra tên phòng ban trùng lặp (nếu thay đổi)
    if (name && name !== department.name) {
      const existingDept = await Department.findOne({ name, _id: { $ne: id } });
      if (existingDept) {
        return res.status(400).json({
          success: false,
          message: "Tên phòng ban đã tồn tại.",
        });
      }
    }

    // Cập nhật manager nếu có thay đổi
    if (managerId !== undefined) {
      // Xóa role manager cũ
      if (department.manager) {
        const oldManager = await User.findById(department.manager);
        if (oldManager) {
          oldManager.rule = 'department_member';
          oldManager.departmentRole = 'member';
          await oldManager.save();
        }
      }

      // Gán manager mới
      if (managerId) {
        const newManager = await User.findById(managerId);
        if (!newManager) {
          return res.status(400).json({
            success: false,
            message: "Không tìm thấy người quản lý được chỉ định.",
          });
        }
        newManager.department = department._id;
        newManager.rule = 'department_manager';
        newManager.departmentRole = 'manager';
        await newManager.save();
      }
    }

    // Cập nhật thông tin phòng ban
    if (name) department.name = name;
    if (description !== undefined) department.description = description;
    if (defaultPermissions) department.defaultPermissions = defaultPermissions;
    if (managerId !== undefined) department.manager = managerId || null;

    ForbiddenError.from(ability).throwUnlessCan("update", department);
    await department.save();

    await department.populate('manager', 'username email');
    await department.populate('createdBy', 'username email');

    res.json({
      success: true,
      message: "Cập nhật phòng ban thành công.",
      department,
    });
  } catch (error) {
    console.error("Error updating department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật phòng ban.",
      error: error.message,
    });
  }
};

// Xóa phòng ban
exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('=== DELETE DEPARTMENT DEBUG ===');
    console.log('ID:', id, 'Length:', id ? id.length : 'undefined');
    console.log('User:', req.user ? { id: req.user._id, rule: req.user.rule, email: req.user.email } : 'undefined');
    console.log('============================');

    // Chỉ admin mới có thể xóa phòng ban
    if (req.user.rule !== 'admin') {
      console.log('Permission denied: user rule is', req.user.rule);
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể xóa phòng ban.",
      });
    }

    // Validate ObjectId
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      console.log('Invalid ID format:', id);
      return res.status(400).json({
        success: false,
        message: `ID phòng ban không hợp lệ. Yêu cầu 24 ký tự hex, nhận được ${id ? id.length : 0} ký tự. ID: ${id}`,
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      console.log('Department not found:', id);
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    console.log('Department found:', { name: department.name });

    // Kiểm tra xem phòng ban có thành viên không
    const memberCount = await User.countDocuments({ department: id });
    console.log('Member count for department:', memberCount);

    // Xử lý thành viên trước khi xóa phòng ban
    if (memberCount > 0) {
      console.log('Department has members, removing department reference from users');

      // Xóa department reference từ tất cả thành viên
      await User.updateMany(
        { department: id },
        {
          $unset: {
            department: 1,
            departmentRole: 1
          },
          rule: 'user' // Đặt lại rule về user thường
        }
      );

      console.log(`Removed department reference from ${memberCount} users`);
    }

    // Soft delete using findByIdAndUpdate to avoid validation issues
    await Department.findByIdAndUpdate(
      id,
      { isActive: false },
      { runValidators: false }
    );

    const message = memberCount > 0
      ? `Xóa phòng ban thành công. ${memberCount} thành viên đã được chuyển về trạng thái không thuộc phòng ban nào.`
      : "Xóa phòng ban thành công.";

    res.json({
      success: true,
      message,
      affectedMembers: memberCount
    });
  } catch (error) {
    console.error("Error deleting department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa phòng ban.",
      error: error.message,
    });
  }
};

// Thêm thành viên vào phòng ban
exports.addMemberToDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId } = req.params;
    const { username, email, password, phonenumber, permissions } = req.body;

    // Kiểm tra quyền
    let department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Fix dept_ prefix in defaultPermissions if exists
    if (department.defaultPermissions && department.defaultPermissions.some(p => p.startsWith('dept_'))) {
      const fixedPermissions = department.defaultPermissions.map(perm => {
        if (perm.startsWith('dept_')) {
          return perm.replace('dept_', '');
        }
        return perm;
      });

      await Department.findByIdAndUpdate(
        departmentId,
        { defaultPermissions: fixedPermissions },
        { validateBeforeSave: false }
      );

      // Refetch department with updated permissions
      department = await Department.findById(departmentId);
    }

    // Kiểm tra quyền thêm thành viên
    const canAddMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       req.user.department &&
       req.user.department.toString() === departmentId);

    if (!canAddMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền thêm thành viên vào phòng ban này.",
      });
    }

    // Kiểm tra email đã tồn tại
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "Địa chỉ email này đã được sử dụng.",
      });
    }

    // Kiểm tra số điện thoại nếu có
    if (phonenumber) {
      const existingPhone = await User.findOne({ phonenumber });
      if (existingPhone) {
        return res.status(400).json({
          success: false,
          message: "Số điện thoại đã được sử dụng.",
        });
      }
    }

    // Tạo mật khẩu tự động nếu không có
    const nanoid = customAlphabet("1234567890abcdef", 8);
    const finalPassword = password || nanoid();
    const hashedPassword = await bcrypt.hash(finalPassword, 10);

    // Use provided permissions or fallback to department defaults
    let finalPermissions = permissions || department.defaultPermissions;

    console.log('Before fix:', finalPermissions);

    // Fix dept_ prefix if exists (for backward compatibility)
    finalPermissions = finalPermissions.map(perm => {
      if (perm.startsWith('dept_')) {
        return perm.replace('dept_', '');
      }
      return perm;
    });

    console.log('After fix:', finalPermissions);

    // Tạo user mới
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      phonenumber,
      code: nanoid(),
      department: departmentId,
      rule: 'department_member',
      departmentRole: 'member',
      permissions: finalPermissions,
    });

    ForbiddenError.from(ability).throwUnlessCan("create", newUser);
    await newUser.save();

    // Cập nhật số lượng thành viên
    await department.updateMemberCount();

    res.status(201).json({
      success: true,
      message: "Thêm thành viên vào phòng ban thành công.",
      user: {
        _id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        phonenumber: newUser.phonenumber,
        rule: newUser.rule,
        departmentRole: newUser.departmentRole,
        permissions: newUser.permissions,
      },
      generatedPassword: password ? null : finalPassword, // Chỉ trả về mật khẩu nếu được tạo tự động
    });
  } catch (error) {
    console.error("Error adding member to department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi thêm thành viên.",
      error: error.message,
    });
  }
};

// Lấy danh sách thành viên phòng ban
exports.getDepartmentMembers = async (req, res) => {

  try {
    const { id: departmentId } = req.params;
    const { page = 1, perPage = 20, search = "" } = req.body;

    // Kiểm tra quyền xem thành viên
    const canViewMembers = req.user.rule === 'admin' ||
      (req.user.department && req.user.department.toString() === departmentId);

    if (!canViewMembers) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xem danh sách thành viên phòng ban này.",
      });
    }

    let filter = { department: departmentId };

    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phonenumber: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * perPage;

    const [members, total] = await Promise.all([
      User.find(filter)
        .select('username email phonenumber rule departmentRole permissions createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(perPage),
      User.countDocuments(filter)
    ]);

    res.json({
      success: true,
      members,
      total,
      page,
      perPage,
    });
  } catch (error) {
    console.error("Error getting department members:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách thành viên.",
      error: error.message,
    });
  }
};

// Cập nhật quyền thành viên phòng ban
exports.updateMemberPermissions = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId, memberId } = req.params;
    const { permissions, departmentRole } = req.body;

    // Kiểm tra quyền cập nhật
    const canUpdateMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       req.user.department &&
       req.user.department.toString() === departmentId);

    if (!canUpdateMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền cập nhật thành viên trong phòng ban này.",
      });
    }

    const member = await User.findOne({ _id: memberId, department: departmentId });
    if (!member) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy thành viên trong phòng ban này.",
      });
    }

    // Không cho phép department manager thay đổi quyền của chính mình
    if (req.user.rule === 'department_manager' && memberId === req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Bạn không thể thay đổi quyền của chính mình.",
      });
    }

    // Cập nhật quyền
    if (permissions) member.permissions = permissions;
    
    // Cập nhật vai trò trong phòng ban
    if (departmentRole) {
      if (departmentRole === 'manager') {
        // Khi thăng cấp thành quản lý phòng ban
        
        // Kiểm tra xem đã có quản lý chưa
        const department = await Department.findById(departmentId);
        if (department && department.manager && department.manager.toString() !== memberId) {
          // Hạ cấp quản lý cũ nếu có
          const oldManager = await User.findById(department.manager);
          if (oldManager) {
            oldManager.rule = 'department_member';
            oldManager.departmentRole = 'member';
            await oldManager.save();
          }
        }
        
        // Thăng cấp thành viên mới
        member.rule = 'department_manager';
        member.departmentRole = 'manager';
        
        // Cập nhật department manager using updateOne to avoid validation issues
        if (department) {
          await Department.updateOne(
            { _id: departmentId },
            { manager: memberId }
          );
        }
      } else {
        // Khi hạ cấp từ quản lý về thành viên
        if (member.rule === 'department_manager') {
          member.rule = 'department_member';
          
          // Xóa manager khỏi department nếu đây là manager hiện tại
          const department = await Department.findById(departmentId);
          if (department && department.manager && department.manager.toString() === memberId) {
            await Department.updateOne(
              { _id: departmentId },
              { manager: null }
            );
          }
        }
        member.departmentRole = departmentRole;
      }
    }

    ForbiddenError.from(ability).throwUnlessCan("update", member);
    await member.save();

    res.json({
      success: true,
      message: "Cập nhật quyền thành viên thành công.",
      member: {
        _id: member._id,
        username: member.username,
        email: member.email,
        permissions: member.permissions,
        departmentRole: member.departmentRole,
      },
    });
  } catch (error) {
    console.error("Error updating member permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật quyền thành viên.",
      error: error.message,
    });
  }
};

// Xóa thành viên khỏi phòng ban
exports.removeMemberFromDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId, memberId } = req.params;

    // Kiểm tra quyền xóa
    const canRemoveMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       req.user.department &&
       req.user.department.toString() === departmentId);

    if (!canRemoveMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xóa thành viên khỏi phòng ban này.",
      });
    }

    const member = await User.findOne({ _id: memberId, department: departmentId });
    if (!member) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy thành viên trong phòng ban này.",
      });
    }

    // Không cho phép department manager xóa chính mình
    if (req.user.rule === 'department_manager' && memberId === req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Bạn không thể xóa chính mình khỏi phòng ban.",
      });
    }

    // Xóa thành viên khỏi phòng ban
    member.department = null;
    member.rule = 'user';
    member.departmentRole = 'member';
    member.permissions = [];

    ForbiddenError.from(ability).throwUnlessCan("update", member);
    await member.save();

    // Cập nhật số lượng thành viên
    const department = await Department.findById(departmentId);
    if (department) {
      await department.updateMemberCount();
    }

    res.json({
      success: true,
      message: "Xóa thành viên khỏi phòng ban thành công.",
    });
  } catch (error) {
    console.error("Error removing member from department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa thành viên.",
      error: error.message,
    });
  }
};

// Cập nhật permission keys cho tất cả phòng ban (utility function)
exports.fixDepartmentPermissions = async (req, res) => {
  try {
    // Chỉ admin mới có thể chạy function này
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể thực hiện thao tác này.",
      });
    }

    const departments = await Department.find({ isActive: true });
    let updatedCount = 0;

    for (const dept of departments) {
      if (dept.defaultPermissions && dept.defaultPermissions.length > 0) {
        // Map dept_ prefixed permissions to non-prefixed ones
        const fixedPermissions = dept.defaultPermissions.map(perm => {
          if (perm.startsWith('dept_')) {
            return perm.replace('dept_', '');
          }
          return perm;
        });

        dept.defaultPermissions = fixedPermissions;
        await dept.save();
        updatedCount++;
      }
    }

    res.json({
      success: true,
      message: `Đã cập nhật permission keys cho ${updatedCount} phòng ban.`,
      updatedCount
    });
  } catch (error) {
    console.error("Error fixing department permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật permission keys.",
      error: error.message,
    });
  }
};

// Cập nhật memberCount cho tất cả phòng ban (utility function)
exports.updateAllMemberCounts = async (req, res) => {
  try {
    // Chỉ admin mới có thể chạy function này
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể thực hiện thao tác này.",
      });
    }

    const departments = await Department.find({ isActive: true });
    let updatedCount = 0;

    for (const dept of departments) {
      await dept.updateMemberCount();
      updatedCount++;
    }

    res.json({
      success: true,
      message: `Đã cập nhật memberCount cho ${updatedCount} phòng ban.`,
      updatedCount
    });
  } catch (error) {
    console.error("Error updating member counts:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật memberCount.",
      error: error.message,
    });
  }
};

// Lấy danh sách quyền có sẵn
exports.getAvailablePermissions = async (_, res) => {
  try {
    const permissions = [
      { key: "user_view", name: "Xem danh sách người dùng", category: "Quản lý người dùng" },
      { key: "user_add", name: "Thêm người dùng mới", category: "Quản lý người dùng" },
      { key: "user_edit", name: "Chỉnh sửa thông tin người dùng", category: "Quản lý người dùng" },
      { key: "user_delete", name: "Xóa người dùng", category: "Quản lý người dùng" },
      { key: "user_import_csv", name: "Nhập danh sách từ file CSV", category: "Quản lý người dùng" },

      { key: "file_view", name: "Xem tài liệu", category: "Quản lý tài liệu" },
      { key: "file_upload", name: "Tải lên tài liệu", category: "Quản lý tài liệu" },
      { key: "file_delete", name: "Xóa tài liệu", category: "Quản lý tài liệu" },

      { key: "court_case_view", name: "Xem hồ sơ vụ án", category: "Quản lý vụ án" },
      { key: "court_case_create", name: "Tạo hồ sơ vụ án mới", category: "Quản lý vụ án" },
      { key: "court_case_edit", name: "Chỉnh sửa hồ sơ vụ án", category: "Quản lý vụ án" },
      { key: "court_case_delete", name: "Xóa hồ sơ vụ án", category: "Quản lý vụ án" },
      { key: "court_case_export", name: "Xuất báo cáo vụ án", category: "Quản lý vụ án" },
      { key: "court_case_import", name: "Nhập dữ liệu vụ án", category: "Quản lý vụ án" },
      { key: "court_case_stats_view", name: "Xem thống kê vụ án", category: "Quản lý vụ án" },
      { key: "court_case_detailed_stats_view", name: "Xem thống kê chi tiết", category: "Quản lý vụ án" },

      { key: "court_case_user_profile_view", name: "Xem hồ sơ người dùng", category: "Quản lý tài khoản" },
      { key: "court_case_user_profile_edit", name: "Chỉnh sửa hồ sơ người dùng", category: "Quản lý tài khoản" },
      { key: "court_case_user_password_change", name: "Đổi mật khẩu người dùng", category: "Quản lý tài khoản" },
      { key: "court_case_user_permissions_view", name: "Xem quyền người dùng", category: "Quản lý tài khoản" },
      { key: "court_case_user_permissions_edit", name: "Chỉnh sửa quyền người dùng", category: "Quản lý tài khoản" },
      { key: "court_case_user_activity_log_view", name: "Xem nhật ký hoạt động", category: "Quản lý tài khoản" },
      { key: "court_case_user_two_factor_manage", name: "Quản lý xác thực 2 yếu tố", category: "Quản lý tài khoản" },



      { key: "system_settings_view", name: "Xem cài đặt hệ thống", category: "Cài đặt hệ thống" },
      { key: "system_settings_edit", name: "Chỉnh sửa cài đặt hệ thống", category: "Cài đặt hệ thống" },


    ];

    res.json({
      success: true,
      permissions,
    });
  } catch (error) {
    console.error("Error getting available permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách quyền.",
      error: error.message,
    });
  }
};