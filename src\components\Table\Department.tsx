"use client";
import { useEffect, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
} from "@tanstack/react-table";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import Pagination from "@/components/Widget/Pagination";
import { Badge } from "@/components/ui/Badge";
import { Edit, Trash2, Users, Plus } from "react-feather";

export default function DepartmentTable() {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const perPage = 10;
  const router = useRouter();

  const columns = [
    {
      accessorKey: "name",
      header: "Tên phòng ban",
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          {row.original.description && (
            <div className="text-sm text-gray-500 mt-1">
              {row.original.description}
            </div>
          )}
        </div>
      ),
    },
    {
      accessorKey: "manager",
      header: "Quản lý",
      cell: ({ row }: any) => (
        <div>
          {row.original.manager ? (
            <div>
              <div className="font-medium">{row.original.manager.username}</div>
              <div className="text-sm text-gray-500">
                {row.original.manager.email}
              </div>
            </div>
          ) : (
            <span className="text-gray-400 italic">Chưa có quản lý</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "memberCount",
      header: "Số thành viên",
      cell: ({ row }: any) => (
        <div className="flex items-center gap-2">
          <Users size={16} className="text-gray-500" />
          <span className="font-medium">{row.original.memberCount}</span>
        </div>
      ),
    },
    {
      accessorKey: "defaultPermissions",
      header: "Quyền mặc định",
      cell: ({ row }: any) => (
        <div className="flex flex-wrap gap-1">
          {row.original.defaultPermissions.length > 0 ? (
            <>
              <Badge variant="secondary" className="text-xs">
                {row.original.defaultPermissions.length} quyền
              </Badge>
            </>
          ) : (
            <span className="text-gray-400 italic text-sm">Không có quyền</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "isActive",
      header: "Trạng thái",
      cell: ({ row }: any) => (
        <Badge variant={row.original.isActive ? "success" : "danger"}>
          {row.original.isActive ? "Hoạt động" : "Không hoạt động"}
        </Badge>
      ),
    },
    {
      id: "actions",
      header: "Thao tác",
      cell: ({ row }: any) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push(`/dashboard/departments/${row.original._id}`)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Xem chi tiết"
          >
            <Users size={16} />
          </button>
          <button
            onClick={() => router.push(`/dashboard/departments/${row.original._id}/edit`)}
            className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
            title="Chỉnh sửa"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => handleDelete(row.original._id)}
            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="Xóa"
          >
            <Trash2 size={16} />
          </button>
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data: departments,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const fetchDepartments = async (page: number, search: string = "") => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartments(
        { page, perPage, search },
        sessionToken
      );

      if (result.payload.success) {
        setDepartments(result.payload.departments);
        setTotal(result.payload.total);
      } else {
        toast.error("Không thể tải danh sách phòng ban");
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
      toast.error("Lỗi khi tải danh sách phòng ban");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    // Find the department to get member count
    const department = departments.find(d => d._id === id);
    const memberCount = department?.memberCount || 0;

    let confirmMessage = "Bạn có chắc chắn muốn xóa phòng ban này?";

    if (memberCount > 0) {
      confirmMessage = `⚠️ CẢNH BÁO: Phòng ban này có ${memberCount} thành viên!\n\n` +
        `Khi xóa phòng ban:\n` +
        `• Tất cả ${memberCount} thành viên sẽ bị xóa khỏi phòng ban\n` +
        `• Họ sẽ trở thành người dùng thường (không thuộc phòng ban nào)\n` +
        `• Các quyền liên quan đến phòng ban sẽ bị mất\n\n` +
        `Bạn có chắc chắn muốn tiếp tục?`;
    }

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.deleteDepartment(id, sessionToken);

      if (result.payload.success) {
        if (result.payload.affectedMembers > 0) {
          toast.success(
            `Xóa phòng ban thành công! ${result.payload.affectedMembers} thành viên đã được chuyển về trạng thái không thuộc phòng ban.`,
            { autoClose: 5000 }
          );
        } else {
          toast.success("Xóa phòng ban thành công");
        }
        fetchDepartments(currentPage, searchTerm);
      } else {
        toast.error(result.payload.message || "Không thể xóa phòng ban");
      }
    } catch (error) {
      console.error("Error deleting department:", error);
      toast.error("Lỗi khi xóa phòng ban");
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchDepartments(1, searchTerm);
  };

  useEffect(() => {
    fetchDepartments(currentPage, searchTerm);
  }, [currentPage]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Quản lý phòng ban
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Tổng cộng {total} phòng ban
          </p>
        </div>
        <button
          onClick={() => router.push("/dashboard/departments/add")}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} />
          Thêm phòng ban
        </button>
      </div>

      {/* Search */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <input
          type="text"
          placeholder="Tìm kiếm theo tên, mã hoặc mô tả..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          type="submit"
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Tìm kiếm
        </button>
      </form>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="hover:bg-gray-50">
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="px-6 py-4 whitespace-nowrap">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>

        {departments.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            Không có phòng ban nào
          </div>
        )}
      </div>

      {/* Pagination */}
      {total > perPage && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(total / perPage)}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
}
