(()=>{var a={};a.id=1035,a.ids=[1035],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3773:(a,b,c)=>{"use strict";c.d(b,{ri:()=>e});var d=c(41597);d.Ay.object({message:d.Ay.string()}).strict();let e=d.Ay.object({title:d.Ay.string().min(1,"Title is required"),desc:d.Ay.string().optional(),address:d.Ay.string().optional(),email:d.Ay.string().optional(),hotline:d.Ay.string().optional(),contact:d.Ay.string().optional(),copyright:d.Ay.string().optional(),footerBLock1:d.Ay.string().optional(),footerBLock2:d.Ay.string().optional(),logo:d.Ay.object({_id:d.Ay.string(),path:d.Ay.string(),folder:d.Ay.string()}),ads1:d.Ay.string().optional(),openReg:d.Ay.boolean().optional()});d.Ay.object({_id:d.Ay.string().optional(),name:d.Ay.string().min(1,"Name is required"),slug:d.Ay.string().min(1,"Slug is required"),tasks:d.Ay.array(d.Ay.any()).optional(),id:d.Ay.number(),droppable:d.Ay.boolean(),parent:d.Ay.number(),text:d.Ay.string().min(1,"Name is required")}),d.Ay.object({_id:d.Ay.string().optional(),title:d.Ay.string(),position:d.Ay.number()})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16604:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={postMedia:(a,b)=>d.Ay.post("api/media/product",a,{headers:{Authorization:`Bearer ${b}`}}),postLogo:(a,b)=>d.Ay.post("api/media/single-noresize",a,{headers:{Authorization:`Bearer ${b}`}}),postFileMedia:(a,b)=>d.Ay.post("api/media/file",a,{headers:{Authorization:`Bearer ${b}`}}),postVideo:(a,b)=>d.Ay.post("api/video/upload",a,{headers:{Authorization:`Bearer ${b}`}})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22960:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),e().createElement("circle",{cx:"12",cy:"12",r:"3"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Eye";let j=i},24241:(a,b,c)=>{Promise.resolve().then(c.bind(c,31365))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31365:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\app\\\\(private)\\\\dashboard\\\\setting\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\setting\\page.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41597:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d={};c.r(d),c.d(d,{BRAND:()=>h.qt,DIRTY:()=>f.jm,EMPTY_PATH:()=>f.I3,INVALID:()=>f.uY,NEVER:()=>h.tm,OK:()=>f.OK,ParseStatus:()=>f.MY,Schema:()=>h.Sj,ZodAny:()=>h.Ml,ZodArray:()=>h.n,ZodBigInt:()=>h.Lr,ZodBoolean:()=>h.WF,ZodBranded:()=>h.eN,ZodCatch:()=>h.hw,ZodDate:()=>h.aP,ZodDefault:()=>h.Xi,ZodDiscriminatedUnion:()=>h.jv,ZodEffects:()=>h.k1,ZodEnum:()=>h.Vb,ZodError:()=>i.G,ZodFirstPartyTypeKind:()=>h.kY,ZodFunction:()=>h.CZ,ZodIntersection:()=>h.Jv,ZodIssueCode:()=>i.eq,ZodLazy:()=>h.Ih,ZodLiteral:()=>h.DN,ZodMap:()=>h.Ut,ZodNaN:()=>h.Tq,ZodNativeEnum:()=>h.WM,ZodNever:()=>h.iS,ZodNull:()=>h.PQ,ZodNullable:()=>h.l1,ZodNumber:()=>h.rS,ZodObject:()=>h.bv,ZodOptional:()=>h.Ii,ZodParsedType:()=>g.Zp,ZodPipeline:()=>h._c,ZodPromise:()=>h.$i,ZodReadonly:()=>h.EV,ZodRecord:()=>h.b8,ZodSchema:()=>h.lK,ZodSet:()=>h.Kz,ZodString:()=>h.ND,ZodSymbol:()=>h.K5,ZodTransformer:()=>h.BG,ZodTuple:()=>h.y0,ZodType:()=>h.aR,ZodUndefined:()=>h._Z,ZodUnion:()=>h.fZ,ZodUnknown:()=>h._,ZodVoid:()=>h.a0,addIssueToContext:()=>f.zn,any:()=>h.bz,array:()=>h.YO,bigint:()=>h.o,boolean:()=>h.zM,coerce:()=>h.au,custom:()=>h.Ie,date:()=>h.p6,datetimeRegex:()=>h.fm,defaultErrorMap:()=>e.su,discriminatedUnion:()=>h.gM,effect:()=>h.QZ,enum:()=>h.k5,function:()=>h.fH,getErrorMap:()=>e.$W,getParsedType:()=>g.CR,instanceof:()=>h.Nl,intersection:()=>h.E$,isAborted:()=>f.G4,isAsync:()=>f.xP,isDirty:()=>f.DM,isValid:()=>f.fn,late:()=>h.fn,lazy:()=>h.RZ,literal:()=>h.eu,makeIssue:()=>f.y7,map:()=>h.Tj,nan:()=>h.oi,nativeEnum:()=>h.fc,never:()=>h.Zm,null:()=>h.ch,nullable:()=>h.me,number:()=>h.ai,object:()=>h.Ik,objectUtil:()=>g.o6,oboolean:()=>h.yN,onumber:()=>h.p7,optional:()=>h.lq,ostring:()=>h.Di,pipeline:()=>h.Tk,preprocess:()=>h.vk,promise:()=>h.iv,quotelessJson:()=>i.WI,record:()=>h.g1,set:()=>h.hZ,setErrorMap:()=>e.pJ,strictObject:()=>h.re,string:()=>h.Yj,symbol:()=>h.HR,transformer:()=>h.Gu,tuple:()=>h.PV,undefined:()=>h.Vx,union:()=>h.KC,unknown:()=>h.L5,util:()=>g.ZS,void:()=>h.rI});var e=c(95254),f=c(33516),g=c(76611),h=c(49445),i=c(73084);let j=d},44273:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c(94652),g=c(5241),h=c(54307);let i=({serverImageUrl:a,onUploadFeatureImg:b,onDeleteFeatureImg:c})=>{let[i,j]=(0,e.useState)(""),[k,l]=(0,e.useState)(""),[m,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)(a||"");return(0,e.useEffect)(()=>{a&&p(function(a){if(!a)return"";let b=(a.startsWith("/")?a.slice(1):a).replace("server/uploads/","uploads/");return`${h.A.NEXT_PUBLIC_API_ENDPOINT}/${b}`}(a))},[a]),(0,d.jsxs)("div",{className:"w-full block mb-2",children:[(0,d.jsxs)("label",{className:"flex flex-col px-4 py-6 border-dashed text-center border border-gray-400 cursor-pointer",children:[(0,d.jsx)("input",{className:"hidden",type:"file",accept:"image/png, image/jpeg, image/bmp, image/gif",onChange:a=>{let c=a.target.files?.[0];if(!c)return;let d=c.size/4024/4024;if(!c.type.match("image.*"))return void j("Please choose an image file");if(n(!0),j(""),d>1)return void j("Your file is too big! Please select an image under 1MB");let e=URL.createObjectURL(c);l(c.name),p(e),j(""),b(c)}}),(0,d.jsxs)("span",{className:"text-center block",children:[(0,d.jsx)("span",{className:"flex justify-center",children:(0,d.jsx)(f.A,{})}),(0,d.jsx)("span",{className:"file-label",children:"Tải ảnh l\xean..."})]}),k&&(0,d.jsxs)("span",{className:"mt-2 text-sm",children:["Đ\xe3 chọn: ",k]}),i&&(0,d.jsx)("span",{className:"text-red-500 mt-2",children:i})]}),o&&(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)("img",{src:o,alt:"Preview",className:"max-w-full h-auto rounded"})}),(0,d.jsx)("button",{type:"button",onClick:()=>{l(""),p(""),j(""),c()},className:"mt-4 text-sm text-red-600 hover:underline absolute right-2 top-2",children:(0,d.jsx)(g.A,{})})]})]})}},51714:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),e().createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),e().createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),e().createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),e().createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),e().createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),e().createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),e().createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Loader";let j=i},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69467:(a,b,c)=>{"use strict";c.d(b,{b:()=>i});var d=c(43210);c(51215);var e=c(81391),f=c(60687),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,e.TL)(`Primitive.${b}`),g=d.forwardRef((a,d)=>{let{asChild:e,...g}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(e?c:b,{...g,ref:d})});return g.displayName=`Primitive.${b}`,{...a,[b]:g}},{}),h=d.forwardRef((a,b)=>(0,f.jsx)(g.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));h.displayName="Label";var i=h},76607:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["setting",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,31365)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\setting\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\setting\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/setting/page",pathname:"/dashboard/setting",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/setting/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79428:a=>{"use strict";a.exports=require("buffer")},80942:(a,b,c)=>{"use strict";c.d(b,{lV:()=>l,MJ:()=>s,zB:()=>n,eI:()=>q,lR:()=>r,C5:()=>t});var d=c(60687),e=c(43210),f=c(81391),g=c(27605),h=c(4780),i=c(69467);let j=(0,c(24224).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(i.b,{ref:c,className:(0,h.cn)(j(),a),...b}));k.displayName=i.b.displayName;let l=g.Op,m=e.createContext({}),n=({...a})=>(0,d.jsx)(m.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),o=()=>{let a=e.useContext(m),b=e.useContext(p),{getFieldState:c,formState:d}=(0,g.xW)(),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},p=e.createContext({}),q=e.forwardRef(({className:a,...b},c)=>{let f=e.useId();return(0,d.jsx)(p.Provider,{value:{id:f},children:(0,d.jsx)("div",{ref:c,className:(0,h.cn)("mb-4",a),...b})})});q.displayName="FormItem";let r=e.forwardRef(({className:a,...b},c)=>{let{error:e,formItemId:f}=o();return(0,d.jsx)(k,{ref:c,className:(0,h.cn)(e&&"text-destructive",a),htmlFor:f,...b})});r.displayName="FormLabel";let s=e.forwardRef(({...a},b)=>{let{error:c,formItemId:e,formDescriptionId:g,formMessageId:h}=o();return(0,d.jsx)(f.DX,{ref:b,id:e,"aria-describedby":c?`${g} ${h}`:`${g}`,"aria-invalid":!!c,...a})});s.displayName="FormControl",e.forwardRef(({className:a,...b},c)=>{let{formDescriptionId:e}=o();return(0,d.jsx)("p",{ref:c,id:e,className:(0,h.cn)("text-[0.8rem] text-muted-foreground",a),...b})}).displayName="FormDescription";let t=e.forwardRef(({className:a,children:b,...c},e)=>{let{error:f,formMessageId:g}=o(),i=f?String(f?.message):b;return i?(0,d.jsx)("p",{ref:e,id:g,className:(0,h.cn)("text-[0.8rem] font-medium text-red-600",a),...c,children:i}):null});t.displayName="FormMessage"},83253:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>t});var d=c(60687),e=c(558),f=c(27605),g=c(80942),h=c(89667),i=c(43210),j=c(51714),k=c(3773),l=c(15655),m=c(93853),n=c(44273),o=c(16604),p=c(61118);let q=()=>{let[a,b]=(0,i.useState)(!1),[c,q]=(0,i.useState)({}),[r,s]=(0,i.useState)(!1),{refreshSettings:t}=(0,p.i)(),u=(0,f.mN)({resolver:(0,e.u)(k.ri),defaultValues:{title:"",desc:"",address:"",email:"",hotline:"",contact:"",copyright:"",footerBLock1:"",footerBLock2:"",openReg:!0,ads1:"",logo:{path:"",folder:"",_id:""}}}),{reset:v}=u;(0,i.useEffect)(()=>{(async()=>{try{b(!0);let a=localStorage.getItem("sessionToken")||"",c=await l.A.fetchSetting(a);c.payload.success?(q(c.payload.setting),v(c.payload.setting)):console.error("Failed to fetch")}catch(a){console.error("Error fetching:",a)}finally{b(!1)}})()},[v]);let w=async a=>{try{let b=localStorage.getItem("sessionToken")||"",c=await l.A.CraeteSetting(a,b);c.payload.success?(m.oR.success("Th\xe0nh C\xf4ng"),q(c.payload.setting),t()):(m.oR.error("An error occurred during update. Please try again."),console.error("Error creating category:",c.payload.message))}catch(a){console.error("Unexpected error:",a),m.oR.error("An error occurred during update. Please try again.")}},x=async c=>{if(a)return"";b(!0);try{let a=new FormData;a.append("imageFile",c);let b=localStorage.getItem("sessionToken")||"",d=await o.A.postLogo(a,b);if(d)return m.oR.success("Đăng h\xecnh ảnh th\xe0nh c\xf4ng."),setTimeout(()=>{u.setValue("logo",d.payload.featureImg)},3e3),d.payload.featureImg.path||"";return""}catch(a){return m.oR.error("An error occurred during update your profile. Please try again."),""}finally{b(!1)}};return(0,d.jsx)("div",{children:(0,d.jsx)(g.lV,{...u,children:(0,d.jsxs)("form",{onSubmit:u.handleSubmit(w),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,d.jsx)(g.zB,{control:u.control,name:"title",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Ti\xeau đề trang web"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nhập ti\xeau đề",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"desc",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"M\xf4 tả"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nhập m\xf4 tả",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"address",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Địa chỉ"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nhập địa chỉ",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"email",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Email"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nhập email",type:"email",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"hotline",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Hotline"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nhập hotline",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"copyright",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Copyright"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"copyright",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"contact",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Li\xean hệ (HTML)"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,placeholder:"Th\xf4ng tin li\xean hệ ",className:"border p-2 rounded w-full h-32"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"footerBLock1",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Footer Block 1 (HTML)"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,placeholder:"Nội dung Block 1",className:"border p-2 rounded w-full h-32"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"footerBLock2",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Footer Block 2 (HTML)"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,placeholder:"Nội dung Block 2",className:"border p-2 rounded w-full h-32"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"ads1",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Khung quảng c\xe1o 1"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,placeholder:"M\xe3 Quảng c\xe1o",className:"border p-2 rounded w-full h-32"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:u.control,name:"openReg",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:(0,d.jsx)("span",{className:"block mb-4",children:"Cho ph\xe9p th\xe0nh vi\xean đăng k\xfd"})}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("input",{type:"checkbox",checked:a.value,onChange:a.onChange,className:"w-5 h-5 border-gray-300 rounded mr-4"})}),(0,d.jsx)(g.lR,{className:"cursor-pointer",children:"Mở đăng k\xfd"}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"block mb-2",children:"Logo web"}),(0,d.jsx)(n.A,{serverImageUrl:c?.logo?.path,onUploadFeatureImg:x,onDeleteFeatureImg:()=>{u.setValue("logo",{path:"",folder:"",_id:""})}})]})]}),(0,d.jsx)("button",{disabled:a,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:a?(0,d.jsx)(j.A,{className:"animate-spin"}):"Submit"})]})})})};var r=c(98462),s=c(55109);function t(){let{hasPermission:a}=(0,s.S)(),b=a("system_settings_edit");return(0,d.jsxs)(r.default,{requiredPermissions:["system_settings_view","system_settings_edit"],requireAll:!1,children:[(0,d.jsx)("h1",{className:"text-2xl mb-4",children:"C\xe0i đặt"}),!b&&(0,d.jsxs)("div",{className:"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4",children:[(0,d.jsx)("strong",{children:"Lưu \xfd:"}),' Bạn chỉ c\xf3 quyền xem c\xe0i đặt. Để chỉnh sửa, cần li\xean hệ quản trị vi\xean cấp quyền "Chỉnh sửa c\xe0i đặt hệ thống".']}),b?(0,d.jsx)(q,{}):(0,d.jsx)("div",{className:"opacity-60 pointer-events-none",children:(0,d.jsx)(q,{})})]})}},85444:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),e().createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="EyeOff";let j=i},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89393:(a,b,c)=>{Promise.resolve().then(c.bind(c,83253))},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>i});var d=c(60687),e=c(4780),f=c(85444),g=c(22960),h=c(43210);let i=c.n(h)().forwardRef(({className:a,type:b,...c},i)=>{let[j,k]=(0,h.useState)(!1);return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"relative w-full",children:[(0,d.jsx)("input",{type:"password"===b&&j?"text":b,autoComplete:"password"===b?"new-password":"",className:(0,e.cn)("input input-bordered w-full rounded-md",a),ref:i,...c}),"password"===b&&(j?(0,d.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}):(0,d.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}))]})})});i.displayName="Input"},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isLoading:l}=(0,e.S)(),m=(0,f.useRouter)();if(l)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,2415,3581,1178],()=>b(b.s=76607));module.exports=c})();