(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3871],{9424:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115),a=t(38637),s=t.n(a);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,r){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t,n,a={},s=Object.keys(e);for(n=0;n<s.length;n++)t=s[n],r.indexOf(t)>=0||(a[t]=e[t]);return a}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)t=s[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return n.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),n.createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="ArrowLeft";let l=i},11080:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var n=t(12115),a=t(38637),s=t.n(a);function o(){return(o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,r){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t,n,a={},s=Object.keys(e);for(n=0;n<s.length;n++)t=s[n],r.indexOf(t)>=0||(a[t]=e[t]);return a}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)t=s[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return n.createElement("svg",o({ref:r,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),n.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),n.createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Save";let l=i},11725:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(27937);let a={fetchUsers:(e,r)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),getAllUsers:(e,r)=>n.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(r)}}),fetchLogs:(e,r)=>n.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(r)}}),deleteUser:(e,r)=>n.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(r)}}),fetchUserById:(e,r,t)=>n.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(r)},signal:t}),CreateUser:(e,r)=>n.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(r)}}),updateUser:(e,r)=>n.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(r)}}),updatePassUser:(e,r)=>n.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(r)}})}},22765:(e,r,t)=>{Promise.resolve().then(t.bind(t,47829))},47829:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var n=t(95155),a=t(12115),s=t(35695),o=t(38543),i=t(3136),l=t(11725),c=t(87708),d=t(9424),u=t(11080);function p(){let e=(0,s.useRouter)(),r=(0,s.useParams)().id,[t,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!0),[m,y]=(0,a.useState)([]),[b,f]=(0,a.useState)([]),[x,v]=(0,a.useState)({name:"",description:"",defaultPermissions:[],managerId:""});(0,a.useEffect)(()=>{r&&(j(),w(),N())},[r]);let j=async()=>{try{let n=localStorage.getItem("sessionToken")||"",a=await i.A.getDepartmentById(r,n);if(a.payload.success){var t;let e=a.payload.department;v({name:e.name,description:e.description||"",defaultPermissions:e.defaultPermissions,managerId:(null==(t=e.manager)?void 0:t._id)||""})}else o.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}catch(r){console.error("Error fetching department:",r),o.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}finally{g(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken")||"",r=await i.A.getAvailablePermissions(e);r.payload.success&&y(r.payload.permissions)}catch(e){console.error("Error fetching permissions:",e)}},N=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await l.A.getAllUsers({page:1,perPage:100},e);if(t.payload.success){let e=t.payload.users.filter(e=>!e.department||"department_manager"!==e.rule||e.department._id===r);f(e)}}catch(e){console.error("Error fetching users:",e)}},k=async t=>{if(t.preventDefault(),!x.name.trim())return void o.oR.error("Vui l\xf2ng nhập t\xean ph\xf2ng ban");try{p(!0);let t=localStorage.getItem("sessionToken")||"",n=await i.A.updateDepartment(r,x,t);n.payload.success?(o.oR.success("Cập nhật ph\xf2ng ban th\xe0nh c\xf4ng"),e.push("/dashboard/departments/".concat(r))):o.oR.error(n.payload.message||"Kh\xf4ng thể cập nhật ph\xf2ng ban")}catch(e){console.error("Error updating department:",e),o.oR.error("Lỗi khi cập nhật ph\xf2ng ban")}finally{p(!1)}},A=m.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return h?(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,n.jsx)(c.default,{requiredPermission:"admin",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,n.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(d.A,{size:20})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa ph\xf2ng ban"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Cập nhật th\xf4ng tin v\xe0 quyền mặc định"})]})]}),(0,n.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin cơ bản"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean ph\xf2ng ban ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:x.name,onChange:e=>v(r=>({...r,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nhập t\xean ph\xf2ng ban",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),(0,n.jsxs)("select",{value:x.managerId,onChange:e=>v(r=>({...r,managerId:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"",children:"Chọn quản l\xfd ph\xf2ng ban"}),b.map(e=>(0,n.jsxs)("option",{value:e._id,children:[e.username," (",e.email,")"]},e._id))]})]})]}),(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,n.jsx)("textarea",{value:x.description,onChange:e=>v(r=>({...r,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về ph\xf2ng ban"})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền mặc định cho người d\xf9ng"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"C\xe1c quyền n\xe0y sẽ được tự động g\xe1n cho tất cả người d\xf9ng mới của ph\xf2ng ban"}),(0,n.jsx)("div",{className:"space-y-4",children:Object.entries(A).map(e=>{let[r,t]=e;return(0,n.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:r}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:t.map(e=>(0,n.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:x.defaultPermissions.includes(e.key),onChange:r=>{var t,n;return t=e.key,n=r.target.checked,void v(e=>({...e,defaultPermissions:n?[...e.defaultPermissions,t]:e.defaultPermissions.filter(e=>e!==t)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.key))})]},r)})})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)("button",{type:"button",onClick:()=>e.back(),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsxs)("button",{type:"submit",disabled:t,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,n.jsx)(u.A,{size:16}),t?"Đang cập nhật...":"Cập nhật ph\xf2ng ban"]})]})]})]})})}}},e=>{e.O(0,[9268,3235,8543,7617,8441,5964,7358],()=>e(e.s=22765)),_N_E=e.O()}]);