"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader } from "react-feather";
import FileUpload from "@/components/FileUpload";
import { useForm } from "react-hook-form";
import {
  AdminRegisterBody,
  AdminRegisterBodyType,
} from "@/schemaValidations/user.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import departmentApiRequest, { Department, Permission } from "@/apiRequests/department";
import { useAppContext } from "@/app/app-provider";

type UserFormValues = z.infer<typeof AdminRegisterBody>;

type AddFormProps = {
  onSubmit: (data: UserFormValues) => Promise<void>; // Submit handler
};

const AddForm = ({ onSubmit }: AddFormProps) => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const { user } = useAppContext();

  const form = useForm<AdminRegisterBodyType>({
    resolver: zodResolver(AdminRegisterBody),
    defaultValues: {
      email: "",
      username: "",
      password: "",
      phonenumber: "",
      department: "",
      permissions: [],
    },
  });

  useEffect(() => {
    fetchDepartments();
    fetchAvailablePermissions();
  }, []);

  const fetchDepartments = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartments({ page: 1, perPage: 100 }, sessionToken);

      if (result.payload.success) {
        let availableDepartments = result.payload.departments;

        // Nếu user là department manager, chỉ hiển thị phòng ban của họ
        if (user?.rule === 'department_manager' && user?.department) {
          availableDepartments = availableDepartments.filter(dept => dept._id === user.department._id);
        }

        setDepartments(availableDepartments);
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
    }
  };

  const fetchAvailablePermissions = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getAvailablePermissions(sessionToken);

      if (result.payload.success) {
        setAvailablePermissions(result.payload.permissions);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  };

  const handleDepartmentChange = (departmentId: string) => {
    const department = departments.find(d => d._id === departmentId);
    setSelectedDepartment(department || null);

    // Tự động set permissions mặc định của phòng ban
    if (department) {
      form.setValue('permissions', department.defaultPermissions);
    } else {
      form.setValue('permissions', []);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="max-w-[600px] flex-shrink-0 w-full"
        noValidate
      >
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Tên người dùng" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Địa chỉ email" type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Mật khẩu" type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phonenumber"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Số điện thoại" type="text" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Department Selection */}
        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phòng ban</FormLabel>
              <FormControl>
                <select
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    handleDepartmentChange(e.target.value);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Chọn phòng ban</option>
                  {departments.map(dept => (
                    <option key={dept._id} value={dept._id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Permissions Selection */}
        {selectedDepartment && (
          <FormField
            control={form.control}
            name="permissions"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quyền hạn</FormLabel>
                <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                  {availablePermissions.map(permission => (
                    <label key={permission.key} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={field.value?.includes(permission.key) || false}
                        onChange={(e) => {
                          const currentPermissions = field.value || [];
                          if (e.target.checked) {
                            field.onChange([...currentPermissions, permission.key]);
                          } else {
                            field.onChange(currentPermissions.filter(p => p !== permission.key));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{permission.name}</span>
                    </label>
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="mt-2 text-red-500 text-sm font-medium">
          {errorMessage}
        </div>
        <button
          disabled={loading ? true : false}
          type="submit"
          className="btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6"
        >
          {loading ? <Loader className="animate-spin" /> : ""}
          Tạo tài khoản
        </button>
      </form>
    </Form>
  );
};

export default AddForm;
