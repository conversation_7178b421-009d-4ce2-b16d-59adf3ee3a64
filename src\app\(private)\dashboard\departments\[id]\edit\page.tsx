"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { toast } from "react-toastify";
import departmentApiRequest, { Department, Permission } from "@/apiRequests/department";
import userApiRequest from "@/apiRequests/user";
import PermissionGuard from "@/components/PermissionGuard";
import { ArrowLeft, Save } from "react-feather";

interface User {
  _id: string;
  username: string;
  email: string;
}

export default function EditDepartmentPage() {
  const router = useRouter();
  const params = useParams();
  const departmentId = params.id as string;
  
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    defaultPermissions: [] as string[],
    managerId: "",
  });

  useEffect(() => {
    if (departmentId) {
      fetchDepartmentData();
      fetchAvailablePermissions();
      fetchAvailableUsers();
    }
  }, [departmentId]);

  const fetchDepartmentData = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getDepartmentById(departmentId, sessionToken);
      
      if (result.payload.success) {
        const dept = result.payload.department;
        setFormData({
          name: dept.name,
          description: dept.description || "",
          defaultPermissions: dept.defaultPermissions,
          managerId: dept.manager?._id || "",
        });
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error) {
      console.error("Error fetching department:", error);
      toast.error("Lỗi khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchAvailablePermissions = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.getAvailablePermissions(sessionToken);
      
      if (result.payload.success) {
        setAvailablePermissions(result.payload.permissions);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  };

  const fetchAvailableUsers = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.getAllUsers({ page: 1, perPage: 100 }, sessionToken);
      
      if (result.payload.success) {
        // Lọc những user chưa có phòng ban hoặc không phải department_manager
        const eligibleUsers = result.payload.users.filter(
          (user: any) => !user.department || user.rule !== 'department_manager' || user.department._id === departmentId
        );
        setAvailableUsers(eligibleUsers);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error("Vui lòng nhập tên phòng ban");
      return;
    }

    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.updateDepartment(
        departmentId,
        formData,
        sessionToken
      );

      if (result.payload.success) {
        toast.success("Cập nhật phòng ban thành công");
        router.push(`/dashboard/departments/${departmentId}`);
      } else {
        toast.error(result.payload.message || "Không thể cập nhật phòng ban");
      }
    } catch (error) {
      console.error("Error updating department:", error);
      toast.error("Lỗi khi cập nhật phòng ban");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (permissionKey: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      defaultPermissions: checked
        ? [...prev.defaultPermissions, permissionKey]
        : prev.defaultPermissions.filter(p => p !== permissionKey)
    }));
  };

  // Group permissions by category
  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermission="admin">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa phòng ban</h1>
            <p className="text-gray-600 mt-1">Cập nhật thông tin và quyền mặc định</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Thông tin cơ bản
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tên phòng ban <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nhập tên phòng ban"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quản lý phòng ban
                </label>
                <select
                  value={formData.managerId}
                  onChange={(e) => setFormData(prev => ({ ...prev, managerId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Chọn quản lý phòng ban</option>
                  {availableUsers.map(user => (
                    <option key={user._id} value={user._id}>
                      {user.username} ({user.email})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mô tả
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Mô tả về phòng ban"
              />
            </div>
          </div>

          {/* Default Permissions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Quyền mặc định cho người dùng
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Các quyền này sẽ được tự động gán cho tất cả người dùng mới của phòng ban
            </p>

            <div className="space-y-4">
              {Object.entries(groupedPermissions).map(([category, permissions]) => (
                <div key={category} className="border rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">{category}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {permissions.map(permission => (
                      <label key={permission.key} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.defaultPermissions.includes(permission.key)}
                          onChange={(e) => handlePermissionChange(permission.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{permission.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Save size={16} />
              {loading ? "Đang cập nhật..." : "Cập nhật phòng ban"}
            </button>
          </div>
        </form>
      </div>
    </PermissionGuard>
  );
}
