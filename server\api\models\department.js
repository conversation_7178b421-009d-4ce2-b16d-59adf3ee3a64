const mongoose = require("mongoose");
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const { Schema } = mongoose;

const departmentSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      default: "",
      trim: true,
    },

    manager: {
      type: Schema.Types.ObjectId,
      ref: "User",
      default: null,
    },
    defaultPermissions: [
      {
        type: String,
        enum: [
          // User Management
          "user_view",
          "user_add",
          "user_edit",
          "user_delete",
          "user_import_csv",

          // File Management
          "file_view",
          "file_upload",
          "file_delete",

          // Court Case Management
          "court_case_view",
          "court_case_create",
          "court_case_edit",
          "court_case_delete",
          "court_case_export",
          "court_case_import",
          "court_case_stats_view",
          "court_case_detailed_stats_view",



          // System Settings
          "system_settings_view",
          "system_settings_edit",



          // Court Case User Account Management
          "court_case_user_profile_view",
          "court_case_user_profile_edit",
          "court_case_user_password_change",
          "court_case_user_permissions_view",
          "court_case_user_permissions_edit",
          "court_case_user_activity_log_view",
          "court_case_user_two_factor_manage",

          // Permission Management
          "permissions_manage",

          // Department Management (for department managers)
          "department_member_view",
          "department_member_add",
          "department_member_edit",
          "department_member_delete",
          "department_member_permissions",
        ],
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
    memberCount: {
      type: Number,
      default: 0,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  { timestamps: true }
);

// Add plugin for CASL permissions
departmentSchema.plugin(accessibleRecordsPlugin);

// Index for better performance
departmentSchema.index({ name: 1 });
departmentSchema.index({ isActive: 1 });

// Virtual for getting department members
departmentSchema.virtual('members', {
  ref: 'User',
  localField: '_id',
  foreignField: 'department'
});

// Update member count when users are added/removed
departmentSchema.methods.updateMemberCount = async function() {
  const User = mongoose.model('User');
  this.memberCount = await User.countDocuments({ department: this._id });
  return this.save({ validateBeforeSave: false });
};

module.exports = mongoose.model("Department", departmentSchema);