const mongoose = require("mongoose");

const { Schema } = mongoose;
const bcrypt = require("bcryptjs");
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const userSchema = new Schema(
  {
    username: { type: String, required: false },
    phonenumber: { type: String, unique: true, sparse: true, required: false  },
    email: {
      type: String,
      required: true,
      unique: true,
      match:
        // eslint-disable-next-line no-useless-escape
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    },
    password: {
      type: String,
      required: true,
    },
    resetlink: {
      type: String,
      default: "",
    },
    private: {
      type: Boolean,
      default: false,
    },
    isMail: {
      type: Boolean,
      default: false,
    },
    isAuthApp: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: String,
    rule: { type: String, enum: ["admin", "department_manager", "department_member", "user"], default: "user" },
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      default: null,
    },
    departmentRole: {
      type: String,
      enum: ["manager", "member"],
      default: "member",
    },
    permissions: [String], // Temporarily disable enum validation
    point: { type: Number, default: 0 },
    code: { type: String, index: true, unique: true },
    Passcode: { type: Schema.Types.ObjectId, ref: "Passcode" },
    avatar: { type: String },
    address: { type: Schema.Types.ObjectId, ref: "Address" },
    rank: { type: String, enum: ["1", "2", "3", "4", "5"], default: "1" },
    bio: { type: String },
    gender: { type: String, enum: ["Male", "Female", "Not"], default: "Not" },
  },
  { timestamps: true }
);

userSchema.plugin(accessibleRecordsPlugin);

userSchema.methods.comparePassword = function (password) {
  const user = this;
  return bcrypt.compareSync(password, user.password);
};

// Method to get effective permissions (own permissions + department default permissions)
userSchema.methods.getEffectivePermissions = async function() {
  let effectivePermissions = [...(this.permissions || [])];

  // If user belongs to a department, inherit department's default permissions
  if (this.department) {
    await this.populate('department');
    if (this.department && this.department.defaultPermissions) {
      // Merge department permissions with user permissions (avoid duplicates)
      const departmentPermissions = this.department.defaultPermissions;
      effectivePermissions = [...new Set([...effectivePermissions, ...departmentPermissions])];
    }
  }

  return effectivePermissions;
};

// Method to check if user can manage another user (department scope)
userSchema.methods.canManageUser = function(targetUser) {
  // Admin can manage everyone
  if (this.rule === 'admin') return true;

  // Department manager can only manage users in their department
  if (this.rule === 'department_manager' && this.departmentRole === 'manager') {
    return this.department && targetUser.department &&
           this.department.toString() === targetUser.department.toString();
  }

  return false;
};

// Method to check if user is department manager
userSchema.methods.isDepartmentManager = function() {
  return this.rule === 'department_manager' && this.departmentRole === 'manager';
};

// Pre-save middleware to update department member count
userSchema.pre('save', async function(next) {
  if (this.isModified('department')) {
    const Department = mongoose.model('Department');

    // Update old department member count
    if (this._original && this._original.department) {
      const oldDept = await Department.findById(this._original.department);
      if (oldDept) {
        await oldDept.updateMemberCount();
      }
    }

    // Update new department member count
    if (this.department) {
      const newDept = await Department.findById(this.department);
      if (newDept) {
        await newDept.updateMemberCount();
      }
    }
  }
  next();
});

// Store original values for comparison
userSchema.pre('save', function(next) {
  if (!this.isNew) {
    this._original = this.toObject();
  }
  next();
});

userSchema.index({ phonenumber: 1 }, { unique: true, partialFilterExpression: { phonenumber: { $exists: true } } });
userSchema.index({ department: 1 });
userSchema.index({ rule: 1 });
userSchema.index({ departmentRole: 1 });

module.exports = mongoose.model("User", userSchema);
