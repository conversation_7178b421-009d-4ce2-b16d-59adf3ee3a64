(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8152],{17703:(e,t,r)=>{"use strict";r.d(t,{CN:()=>l,Lz:()=>c,Wu:()=>i,ZB:()=>a,Zp:()=>s,aR:()=>o});var n=r(95155);r(12115);let s=e=>{let{children:t,className:r="",padding:s="md",shadow:o="sm",hover:a=!1,onClick:i}=e;return(0,n.jsx)("div",{className:"\n        bg-white rounded-xl border border-gray-100\n        ".concat({none:"",sm:"p-4",md:"p-6",lg:"p-8"}[s],"\n        ").concat({none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[o],"\n        ").concat(a?"hover:shadow-lg transition-shadow duration-200":"","\n        ").concat(r,"\n      "),onClick:i,children:t})},o=e=>{let{children:t,className:r=""}=e;return(0,n.jsx)("div",{className:"border-b border-gray-100 pb-4 mb-6 ".concat(r),children:t})},a=e=>{let{children:t,className:r="",size:s="md"}=e;return(0,n.jsx)("h2",{className:"font-bold text-gray-900 ".concat({sm:"text-lg",md:"text-xl",lg:"text-2xl"}[s]," ").concat(r),children:t})},i=e=>{let{children:t,className:r=""}=e;return(0,n.jsx)("div",{className:r,children:t})},l=e=>{let{title:t,value:r,icon:o,trend:a,color:i="blue",onClick:l,clickable:c=!1}=e,d=(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof r?r.toLocaleString():r}),a&&(0,n.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat(a.isPositive?"text-green-600":"text-red-600"),children:[(0,n.jsx)("span",{className:"mr-1",children:a.isPositive?"↗":"↘"}),a.value]})]}),o&&(0,n.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[i]),children:(0,n.jsx)("div",{className:"text-white",children:o})})]});return c&&l?(0,n.jsx)(s,{hover:!0,className:"relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ".concat(c?"hover:shadow-lg":""),onClick:l,children:d}):(0,n.jsx)(s,{hover:!0,className:"relative overflow-hidden",children:d})},c=e=>{let{title:t,description:r,icon:o,onClick:a,href:i,color:l="blue"}=e,c=e=>{let{children:t}=e;return(0,n.jsx)(s,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:t})},d=(0,n.jsxs)("div",{className:"flex items-start space-x-4",children:[o&&(0,n.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[l]," flex-shrink-0"),children:(0,n.jsx)("div",{className:"text-white",children:o})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:t}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:r})]})]});return i?(0,n.jsx)("a",{href:i,children:(0,n.jsx)(c,{children:d})}):(0,n.jsx)("div",{onClick:a,children:(0,n.jsx)(c,{children:d})})}},18617:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(27937);let s={getDashboardStats:e=>n.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}}),getRecentActivities:(e,t)=>n.Ay.get("/api/administrator/recent-activities".concat(t?"?limit=".concat(t):""),{headers:{Authorization:"Bearer ".concat(e)}}),toggleUserPrivate:(e,t)=>n.Ay.put("/api/administrator/update-private",{id:e},{headers:{Authorization:"Bearer ".concat(t)}})}},21379:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),s=r(38637),o=r.n(s);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,o=void 0===s?24:s,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),n.createElement("circle",{cx:"9",cy:"7",r:"4"}),n.createElement("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),n.createElement("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}))});i.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},i.displayName="Users";let l=i},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var n=r(84559),s=r(59434),o=r(35695);class a extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends a{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?n.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,h=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),g=await fetch(h,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),p=null,m=g.headers.get("content-type");if(m&&m.includes("application/json"))try{p=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await g.text();let f={status:g.status,payload:p};if(!g.ok)if(404===g.status||403===g.status)throw new i(f);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,o.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new a(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},38637:(e,t,r)=>{e.exports=r(79399)()},58973:(e,t,r)=>{Promise.resolve().then(r.bind(r,83889))},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>a,cn:()=>o}),r(27937);var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}r(58801);let a=e=>e.startsWith("/")?e.slice(1):e},64332:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),s=r(38637),o=r.n(s);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,o=void 0===s?24:s,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}))});i.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},i.displayName="Shield";let l=i},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},79399:(e,t,r)=>{"use strict";var n=r(72948);function s(){}function o(){}o.resetWarningCache=s,e.exports=function(){function e(e,t,r,s,o,a){if(a!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:s};return r.PropTypes=r,r}},83889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(95155),s=r(12115),o=r(17703),a=r(18617),i=r(38543),l=r(21379),c=r(92773),d=r(95512),u=r(64332),h=r(38637),g=r.n(h);function p(){return(p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var m=(0,s.forwardRef)(function(e,t){var r=e.color,n=e.size,o=void 0===n?24:n,a=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return s.createElement("svg",p({ref:t,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),s.createElement("polyline",{points:"22 12 18 12 15 21 9 3 6 12 2 12"}))});m.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},m.displayName="Activity";let f=()=>{let[e,t]=(0,s.useState)({totalUsers:0,activeUsers:0}),[r,h]=(0,s.useState)(!0),[g,p]=(0,s.useState)(""),[f,v]=(0,s.useState)(!1),[x,y]=(0,s.useState)([]),[b,j]=(0,s.useState)(!0);(0,s.useEffect)(()=>{p(localStorage.getItem("sessionToken")||"")},[]),(0,s.useEffect)(()=>{if(!g||f)return;let e=setTimeout(async()=>{try{h(!0),console.log("Fetching dashboard stats...");let e=await a.A.getDashboardStats(g);console.log("API Response:",e),e.payload.success?(console.log("Dashboard stats received:",e.payload.stats),t(e.payload.stats),v(!0),i.oR.success("Đ\xe3 tải thống k\xea th\xe0nh c\xf4ng")):i.oR.error("Kh\xf4ng thể tải thống k\xea dashboard")}catch(e){console.error("Error fetching dashboard stats:",e),t({totalUsers:156,activeUsers:89}),v(!0),(null==e?void 0:e.status)===429?i.oR.warning("Qu\xe1 nhiều y\xeau cầu, sử dụng dữ liệu cache"):i.oR.warning("Lỗi API, sử dụng dữ liệu mẫu")}finally{h(!1)}},1e3);return()=>clearTimeout(e)},[g]),(0,s.useEffect)(()=>{if(!g)return;let e=setTimeout(async()=>{try{j(!0);let e=await a.A.getRecentActivities(g,8);e.payload.success?y(e.payload.activities):i.oR.error("Kh\xf4ng thể tải hoạt động gần đ\xe2y")}catch(e){console.error("Error fetching recent activities:",e),(null==e?void 0:e.status)===429?i.oR.warning("Qu\xe1 nhiều y\xeau cầu, vui l\xf2ng thử lại sau"):i.oR.error("Lỗi khi tải hoạt động gần đ\xe2y")}finally{j(!1)}},1500);return()=>clearTimeout(e)},[g]);let w=[{title:"Quản l\xfd th\xe0nh vi\xean",description:"Xem v\xe0 quản l\xfd t\xe0i khoản người d\xf9ng",href:"/dashboard/user",icon:(0,n.jsx)(l.A,{size:20}),color:"green"},{title:"Quản l\xfd vụ việc",description:"Quản l\xfd vụ việc t\xf2a \xe1n v\xe0 thụ l\xfd",href:"/dashboard/court-cases",icon:(0,n.jsx)(c.A,{size:20}),color:"purple"},{title:"C\xe0i đặt hệ thống",description:"Cấu h\xecnh v\xe0 t\xf9y chỉnh website",href:"/dashboard/setting",icon:(0,n.jsx)(d.A,{size:20}),color:"orange"},{title:"Quản l\xfd file",description:"Quản l\xfd t\xe0i liệu v\xe0 h\xecnh ảnh",href:"/dashboard/files",icon:(0,n.jsx)(c.A,{size:20}),color:"blue"}];return(0,n.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,n.jsx)(u.A,{className:"mr-3",size:36}),"Bảng điều khiển quản trị"]}),(0,n.jsx)("p",{className:"text-purple-100 text-lg",children:"Quản l\xfd to\xe0n bộ hệ thống v\xe0 nội dung website"})]}),(0,n.jsx)("div",{className:"hidden md:block",children:(0,n.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:(0,n.jsx)(m,{size:48,className:"text-white"})})})]})}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:r?Array.from({length:6}).map((e,t)=>(0,n.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border animate-pulse",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,n.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]}),(0,n.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"})]})},t)):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.CN,{title:"Th\xe0nh vi\xean",value:e.totalUsers,icon:(0,n.jsx)(l.A,{size:24}),color:"green"}),(0,n.jsx)(o.CN,{title:"Đang hoạt động",value:e.activeUsers,icon:(0,n.jsx)(m,{size:24}),color:"blue"})]})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Thao t\xe1c nhanh"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:w.map((e,t)=>(0,n.jsx)(o.Lz,{title:e.title,description:e.description,href:e.href,icon:e.icon,color:e.color},t))})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Hoạt động gần đ\xe2y"}),(0,n.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Xem tất cả"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:b?Array.from({length:8}).map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse",children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-gray-200"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t)):x.length>0?x.map(e=>{let t=(e=>{let t=new Date,r=new Date(e),n=Math.floor((t.getTime()-r.getTime())/1e3);return n<60?"".concat(n," gi\xe2y trước"):n<3600?"".concat(Math.floor(n/60)," ph\xfat trước"):n<86400?"".concat(Math.floor(n/3600)," giờ trước"):n<2592e3?"".concat(Math.floor(n/86400)," ng\xe0y trước"):"".concat(Math.floor(n/2592e3)," th\xe1ng trước")})(e.time),r=(e=>{switch(e){case"post_created":case"page_created":return{color:"bg-green-500",icon:"create"};case"post_updated":case"page_updated":return{color:"bg-blue-500",icon:"update"};case"post_pending":return{color:"bg-orange-500",icon:"pending"};case"post_pending_updated":return{color:"bg-orange-400",icon:"pending"};case"user_registered":return{color:"bg-purple-500",icon:"user"};default:return{color:"bg-gray-500",icon:"default"}}})(e.type);return(0,n.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100",children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(r.color)}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm text-gray-900",children:e.action}),(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:[e.title&&(0,n.jsx)("span",{className:"font-medium",children:e.title}),e.title&&" • ","bởi ",e.user," • ",t]})]})]},e.id)}):(0,n.jsxs)("div",{className:"col-span-full text-center py-8 text-gray-500",children:[(0,n.jsx)(m,{size:48,className:"mx-auto mb-2 opacity-50"}),(0,n.jsx)("p",{children:"Chưa c\xf3 hoạt động n\xe0o"})]})})]})]})})}},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(74556),s=r(49509);let o=n.Ik({NEXT_PUBLIC_API_ENDPOINT:n.Yj().url(),NEXT_PUBLIC_URL:n.Yj().url(),CRYPTOJS_SECRECT:n.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!o.success)throw console.error("Invalid environment variables:",o.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let a=o.data},92773:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),s=r(38637),o=r.n(s);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,o=void 0===s?24:s,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),n.createElement("polyline",{points:"14 2 14 8 20 8"}),n.createElement("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),n.createElement("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),n.createElement("polyline",{points:"10 9 9 9 8 9"}))});i.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},i.displayName="FileText";let l=i},95512:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(12115),s=r(38637),o=r.n(s);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var i=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,o=void 0===s?24:s,i=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",a({ref:t,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),n.createElement("circle",{cx:"12",cy:"12",r:"3"}),n.createElement("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}))});i.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},i.displayName="Settings";let l=i}},e=>{e.O(0,[9268,3235,8543,8441,5964,7358],()=>e(e.s=58973)),_N_E=e.O()}]);