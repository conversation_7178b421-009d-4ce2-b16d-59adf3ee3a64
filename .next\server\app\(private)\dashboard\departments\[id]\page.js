(()=>{var a={};a.id=22,a.ids=[22],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5597:(a,b,c)=>{Promise.resolve().then(c.bind(c,44127))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16500:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(9113);let e={createDepartment:(a,b)=>d.Ay.post("/api/departments",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartments:(a,b)=>d.Ay.post("/api/departments/list",a,{headers:{Authorization:`Bearer ${b}`}}),getDepartmentById:(a,b)=>d.Ay.get(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateDepartment:(a,b,c)=>d.Ay.put(`/api/departments/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteDepartment:(a,b)=>d.Ay.delete(`/api/departments/${a}`,{headers:{Authorization:`Bearer ${b}`}}),addMemberToDepartment:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members`,b,{headers:{Authorization:`Bearer ${c}`}}),getDepartmentMembers:(a,b,c)=>d.Ay.post(`/api/departments/${a}/members/list`,b,{headers:{Authorization:`Bearer ${c}`}}),updateMemberPermissions:(a,b,c,e)=>d.Ay.put(`/api/departments/${a}/members/${b}`,c,{headers:{Authorization:`Bearer ${e}`}}),removeMemberFromDepartment:(a,b,c)=>d.Ay.delete(`/api/departments/${a}/members/${b}`,{headers:{Authorization:`Bearer ${c}`}}),getAvailablePermissions:a=>d.Ay.get("/api/departments/permissions",{headers:{Authorization:`Bearer ${a}`}})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44127:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(60687),e=c(43210),f=c.n(e),g=c(16189),h=c(93853),i=c(16500),j=c(98462),k=c(96800),l=c(87955),m=c.n(l);function n(){return(n=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var o=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",n({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("polyline",{points:"23 4 23 10 17 10"}),f().createElement("polyline",{points:"1 20 1 14 7 14"}),f().createElement("path",{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"}))});o.propTypes={color:m().string,size:m().oneOfType([m().string,m().number])},o.displayName="RefreshCw";var p=c(46345),q=c(76957),r=c(69545),s=c(70628),t=c(71170);let u=new Map;function v(){let a=(0,g.useRouter)(),b=(0,g.useParams)().id,[c,f]=(0,e.useState)(null),[l,m]=(0,e.useState)([]),[n,v]=(0,e.useState)(!0),[w,x]=(0,e.useState)(!1),[y,z]=(0,e.useState)(0),A=async()=>{try{v(!0);let c=localStorage.getItem("sessionToken")||"",d=await i.A.getDepartmentById(b,c);d.payload.success?f(d.payload.department):(h.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),a.push("/dashboard/departments"))}catch(b){console.error("Error fetching department:",b),h.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),a.push("/dashboard/departments")}finally{v(!1)}},B=async()=>{try{x(!0);let a=localStorage.getItem("sessionToken")||"",c=await i.A.getDepartmentMembers(b,{page:1,perPage:50},a);c.payload.success&&m(c.payload.members)}catch(a){console.error("Error fetching members:",a)}finally{x(!1)}};return n?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):c?(0,d.jsx)(j.default,{requiredPermission:"admin",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("button",{onClick:()=>a.push("/dashboard/departments"),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(k.A,{size:20})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:c.name}),c.description&&(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:c.description})]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>{z(a=>a+1),A(),B()},className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"L\xe0m mới dữ liệu",children:(0,d.jsx)(o,{size:16})}),(0,d.jsxs)("button",{onClick:()=>a.push(`/dashboard/departments/${b}/edit`),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(p.A,{size:16}),"Chỉnh sửa"]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin ph\xf2ng ban"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xean ph\xf2ng ban"}),(0,d.jsx)("p",{className:"text-gray-900",children:c.name})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),c.manager?(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:c.manager.username}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:c.manager.email})]}):(0,d.jsx)("p",{className:"text-gray-400 italic",children:"Chưa c\xf3 quản l\xfd"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số th\xe0nh vi\xean"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{size:16,className:"text-gray-500"}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:l.length})]})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,d.jsx)("p",{className:"text-gray-900",children:c.description||"Kh\xf4ng c\xf3 m\xf4 tả"})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Quyền mặc định (",c.defaultPermissions.length," quyền)"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:c.defaultPermissions.length>0?c.defaultPermissions.map(a=>(0,d.jsx)(t.Ex,{variant:"secondary",className:"text-xs",children:function(a){let b=u.get(a);return b?b.name:a}(a)},a)):(0,d.jsx)("p",{className:"text-gray-400 italic",children:"Kh\xf4ng c\xf3 quyền mặc định"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trạng th\xe1i"}),(0,d.jsx)(t.Ex,{variant:c.isActive?"success":"danger",children:c.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xe0y tạo"}),(0,d.jsx)("p",{className:"text-gray-900",children:new Date(c.createdAt).toLocaleDateString("vi-VN")})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["Danh s\xe1ch th\xe0nh vi\xean (",l.length,")"]}),(0,d.jsxs)("button",{onClick:()=>a.push(`/dashboard/departments/${b}/members/add`),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)(r.A,{size:16}),"Th\xeam th\xe0nh vi\xean"]})]})}),(0,d.jsx)("div",{className:"p-6",children:w?(0,d.jsx)("div",{className:"flex justify-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):l.length>0?(0,d.jsx)("div",{className:"space-y-4",children:l.map(c=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,d.jsx)("div",{className:"flex items-center gap-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900",children:c.username}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:c.email}),c.phonenumber&&(0,d.jsx)("p",{className:"text-sm text-gray-500",children:c.phonenumber})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)(t.eG,{role:c.rule}),c.departmentRole&&"member"!==c.departmentRole&&(0,d.jsx)("div",{className:"mt-1",children:(0,d.jsx)(t.Ex,{variant:"info",className:"text-xs",children:"manager"===c.departmentRole?"Quản l\xfd":c.departmentRole})}),(0,d.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[c.permissions.length," quyền"]})]}),(0,d.jsx)("button",{onClick:()=>a.push(`/dashboard/departments/${b}/members/${c._id}/edit`),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa quyền",children:(0,d.jsx)(s.A,{size:16})})]})]},c._id))}):(0,d.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Chưa c\xf3 th\xe0nh vi\xean n\xe0o trong ph\xf2ng ban"})})]})]})}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy ph\xf2ng ban"})})}[{id:"user_view",name:"Xem danh s\xe1ch người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin người d\xf9ng"},{id:"user_add",name:"Th\xeam người d\xf9ng mới",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể tạo t\xe0i khoản người d\xf9ng mới"},{id:"user_edit",name:"Chỉnh sửa người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể chỉnh sửa th\xf4ng tin người d\xf9ng"},{id:"user_delete",name:"X\xf3a người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể x\xf3a t\xe0i khoản người d\xf9ng"},{id:"user_import_csv",name:"Nhập th\xe0nh vi\xean từ CSV",category:"Quản l\xfd th\xe0nh vi\xean",description:"C\xf3 thể nhập danh s\xe1ch th\xe0nh vi\xean từ file CSV"},{id:"file_view",name:"Xem danh s\xe1ch file",category:"Quản l\xfd file",description:"C\xf3 thể xem danh s\xe1ch file v\xe0 t\xe0i liệu"},{id:"file_upload",name:"Tải l\xean file",category:"Quản l\xfd file",description:"C\xf3 thể tải l\xean file v\xe0 t\xe0i liệu mới"},{id:"file_delete",name:"X\xf3a file",category:"Quản l\xfd file",description:"C\xf3 thể x\xf3a file v\xe0 t\xe0i liệu"},{id:"court_case_view",name:"Xem danh s\xe1ch vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_create",name:"Tạo vụ việc mới",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể tạo vụ việc t\xf2a \xe1n mới"},{id:"court_case_edit",name:"Chỉnh sửa vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể chỉnh sửa th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_delete",name:"X\xf3a vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể x\xf3a vụ việc t\xf2a \xe1n"},{id:"court_case_export",name:"Xuất dữ liệu vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xuất danh s\xe1ch vụ việc ra file Excel/CSV"},{id:"court_case_import",name:"Nhập dữ liệu vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể nhập danh s\xe1ch vụ việc từ file Excel/CSV"},{id:"court_case_stats_view",name:"Xem thống k\xea vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem b\xe1o c\xe1o v\xe0 thống k\xea vụ việc t\xf2a \xe1n"},{id:"court_case_detailed_stats_view",name:"Xem thống k\xea chi tiết",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem thống k\xea chi tiết v\xe0 n\xe2ng cao"},{id:"court_case_print",name:"In vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể in th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_user_profile_view",name:"Xem hồ sơ người d\xf9ng",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"Xem th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"court_case_user_profile_edit",name:"Chỉnh sửa hồ sơ người d\xf9ng",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"Chỉnh sửa th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"news_view",name:"Xem tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể xem danh s\xe1ch tin tức v\xe0 b\xe0i viết"},{id:"news_create",name:"Tạo tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể tạo tin tức v\xe0 b\xe0i viết mới"},{id:"news_edit",name:"Chỉnh sửa tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể chỉnh sửa tin tức v\xe0 b\xe0i viết"},{id:"news_delete",name:"X\xf3a tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể x\xf3a tin tức v\xe0 b\xe0i viết"},{id:"system_settings_view",name:"Xem c\xe0i đặt hệ thống",category:"Quản l\xfd hệ thống",description:"C\xf3 thể xem c\xe1c c\xe0i đặt hệ thống"},{id:"system_settings_edit",name:"Chỉnh sửa c\xe0i đặt hệ thống",category:"Quản l\xfd hệ thống",description:"C\xf3 thể chỉnh sửa c\xe1c c\xe0i đặt hệ thống"},{id:"analytics_view",name:"Xem ph\xe2n t\xedch",category:"Quản l\xfd hệ thống",description:"C\xf3 thể xem c\xe1c b\xe1o c\xe1o ph\xe2n t\xedch hệ thống"}].forEach(a=>{u.set(a.id,a)})},46345:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),e().createElement("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Edit";let j=i},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69545:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),e().createElement("line",{x1:"5",y1:"12",x2:"19",y2:"12"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Plus";let j=i},71170:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(60687);c(43210);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}}[a];return(0,d.jsx)(e,{variant:c.variant,className:b,children:c.label})}},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87037:(a,b,c)=>{Promise.resolve().then(c.bind(c,91282))},87503:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["departments",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,91282)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\departments\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\departments\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/departments/[id]/page",pathname:"/dashboard/departments/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/departments/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},91282:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\app\\\\(private)\\\\dashboard\\\\departments\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\departments\\[id]\\page.tsx","default")},96800:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),e().createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="ArrowLeft";let j=i},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isLoading:l}=(0,e.S)(),m=(0,f.useRouter)();if(l)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,3581,1178],()=>b(b.s=87503));module.exports=c})();