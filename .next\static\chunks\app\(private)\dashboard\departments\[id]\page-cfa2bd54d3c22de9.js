(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{8509:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(12115),i=n(38637),a=n.n(i);function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=(0,r.forwardRef)(function(e,t){var n=e.color,i=e.size,a=void 0===i?24:i,l=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return r.createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),r.createElement("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),r.createElement("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Edit";let c=l},9424:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(12115),i=n(38637),a=n.n(i);function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=(0,r.forwardRef)(function(e,t){var n=e.color,i=e.size,a=void 0===i?24:i,l=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return r.createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),r.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),r.createElement("polyline",{points:"12 19 5 12 12 5"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="ArrowLeft";let c=l},14638:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>b});var r=n(95155),i=n(12115),a=n(35695),s=n(38543),l=n(3136),c=n(87708),o=n(9424),d=n(38637),h=n.n(d);function m(){return(m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var g=(0,i.forwardRef)(function(e,t){var n=e.color,r=e.size,a=void 0===r?24:r,s=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return i.createElement("svg",m({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),i.createElement("polyline",{points:"23 4 23 10 17 10"}),i.createElement("polyline",{points:"1 20 1 14 7 14"}),i.createElement("path",{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"}))});g.propTypes={color:h().string,size:h().oneOfType([h().string,h().number])},g.displayName="RefreshCw";var u=n(8509),p=n(21379),v=n(68661),x=n(95512),f=n(52814);let y=new Map;function b(){let e=(0,a.useRouter)(),t=(0,a.useParams)().id,[n,d]=(0,i.useState)(null),[h,m]=(0,i.useState)([]),[b,j]=(0,i.useState)(!0),[w,N]=(0,i.useState)(!1),[O,_]=(0,i.useState)(0);(0,i.useEffect)(()=>{t&&(C(),E())},[t,O]);let k=()=>{_(e=>e+1),C(),E()};(0,i.useEffect)(()=>{let e=()=>{!document.hidden&&t&&k()};return document.addEventListener("visibilitychange",e),window.addEventListener("focus",e),()=>{document.removeEventListener("visibilitychange",e),window.removeEventListener("focus",e)}},[t]);let C=async()=>{try{j(!0);let n=localStorage.getItem("sessionToken")||"",r=await l.A.getDepartmentById(t,n);r.payload.success?d(r.payload.department):(s.oR.error("Kh\xf4ng thể tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments"))}catch(t){console.error("Error fetching department:",t),s.oR.error("Lỗi khi tải th\xf4ng tin ph\xf2ng ban"),e.push("/dashboard/departments")}finally{j(!1)}},E=async()=>{try{N(!0);let e=localStorage.getItem("sessionToken")||"",n=await l.A.getDepartmentMembers(t,{page:1,perPage:50},e);n.payload.success&&m(n.payload.members)}catch(e){console.error("Error fetching members:",e)}finally{N(!1)}};return b?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):n?(0,r.jsx)(c.default,{requiredPermission:"admin",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:()=>e.push("/dashboard/departments"),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(o.A,{size:20})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:n.name}),n.description&&(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:n.description})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:k,className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"L\xe0m mới dữ liệu",children:(0,r.jsx)(g,{size:16})}),(0,r.jsxs)("button",{onClick:()=>e.push("/dashboard/departments/".concat(t,"/edit")),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(u.A,{size:16}),"Chỉnh sửa"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin ph\xf2ng ban"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xean ph\xf2ng ban"}),(0,r.jsx)("p",{className:"text-gray-900",children:n.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quản l\xfd ph\xf2ng ban"}),n.manager?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:n.manager.username}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:n.manager.email})]}):(0,r.jsx)("p",{className:"text-gray-400 italic",children:"Chưa c\xf3 quản l\xfd"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số th\xe0nh vi\xean"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{size:16,className:"text-gray-500"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:h.length})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,r.jsx)("p",{className:"text-gray-900",children:n.description||"Kh\xf4ng c\xf3 m\xf4 tả"})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Quyền mặc định (",n.defaultPermissions.length," quyền)"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:n.defaultPermissions.length>0?n.defaultPermissions.map(e=>(0,r.jsx)(f.Ex,{variant:"secondary",className:"text-xs",children:function(e){let t=y.get(e);return t?t.name:e}(e)},e)):(0,r.jsx)("p",{className:"text-gray-400 italic",children:"Kh\xf4ng c\xf3 quyền mặc định"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trạng th\xe1i"}),(0,r.jsx)(f.Ex,{variant:n.isActive?"success":"danger",children:n.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ng\xe0y tạo"}),(0,r.jsx)("p",{className:"text-gray-900",children:new Date(n.createdAt).toLocaleDateString("vi-VN")})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["Danh s\xe1ch th\xe0nh vi\xean (",h.length,")"]}),(0,r.jsxs)("button",{onClick:()=>e.push("/dashboard/departments/".concat(t,"/members/add")),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,r.jsx)(v.A,{size:16}),"Th\xeam th\xe0nh vi\xean"]})]})}),(0,r.jsx)("div",{className:"p-6",children:w?(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):h.length>0?(0,r.jsx)("div",{className:"space-y-4",children:h.map(n=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:n.username}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:n.email}),n.phonenumber&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:n.phonenumber})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)(f.eG,{role:n.rule}),n.departmentRole&&"member"!==n.departmentRole&&(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(f.Ex,{variant:"info",className:"text-xs",children:"manager"===n.departmentRole?"Quản l\xfd":n.departmentRole})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[n.permissions.length," quyền"]})]}),(0,r.jsx)("button",{onClick:()=>e.push("/dashboard/departments/".concat(t,"/members/").concat(n._id,"/edit")),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa quyền",children:(0,r.jsx)(x.A,{size:16})})]})]},n._id))}):(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Chưa c\xf3 th\xe0nh vi\xean n\xe0o trong ph\xf2ng ban"})})]})]})}):(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy ph\xf2ng ban"})})}[{id:"user_view",name:"Xem danh s\xe1ch người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin người d\xf9ng"},{id:"user_add",name:"Th\xeam người d\xf9ng mới",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể tạo t\xe0i khoản người d\xf9ng mới"},{id:"user_edit",name:"Chỉnh sửa người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể chỉnh sửa th\xf4ng tin người d\xf9ng"},{id:"user_delete",name:"X\xf3a người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể x\xf3a t\xe0i khoản người d\xf9ng"},{id:"user_import_csv",name:"Nhập th\xe0nh vi\xean từ CSV",category:"Quản l\xfd th\xe0nh vi\xean",description:"C\xf3 thể nhập danh s\xe1ch th\xe0nh vi\xean từ file CSV"},{id:"file_view",name:"Xem danh s\xe1ch file",category:"Quản l\xfd file",description:"C\xf3 thể xem danh s\xe1ch file v\xe0 t\xe0i liệu"},{id:"file_upload",name:"Tải l\xean file",category:"Quản l\xfd file",description:"C\xf3 thể tải l\xean file v\xe0 t\xe0i liệu mới"},{id:"file_delete",name:"X\xf3a file",category:"Quản l\xfd file",description:"C\xf3 thể x\xf3a file v\xe0 t\xe0i liệu"},{id:"court_case_view",name:"Xem danh s\xe1ch vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_create",name:"Tạo vụ việc mới",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể tạo vụ việc t\xf2a \xe1n mới"},{id:"court_case_edit",name:"Chỉnh sửa vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể chỉnh sửa th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_delete",name:"X\xf3a vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể x\xf3a vụ việc t\xf2a \xe1n"},{id:"court_case_export",name:"Xuất dữ liệu vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xuất danh s\xe1ch vụ việc ra file Excel/CSV"},{id:"court_case_import",name:"Nhập dữ liệu vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể nhập danh s\xe1ch vụ việc từ file Excel/CSV"},{id:"court_case_stats_view",name:"Xem thống k\xea vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem b\xe1o c\xe1o v\xe0 thống k\xea vụ việc t\xf2a \xe1n"},{id:"court_case_detailed_stats_view",name:"Xem thống k\xea chi tiết",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể xem thống k\xea chi tiết v\xe0 n\xe2ng cao"},{id:"court_case_print",name:"In vụ việc",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"C\xf3 thể in th\xf4ng tin vụ việc t\xf2a \xe1n"},{id:"court_case_user_profile_view",name:"Xem hồ sơ người d\xf9ng",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"Xem th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"court_case_user_profile_edit",name:"Chỉnh sửa hồ sơ người d\xf9ng",category:"Quản l\xfd vụ việc t\xf2a \xe1n",description:"Chỉnh sửa th\xf4ng tin hồ sơ người d\xf9ng trong hệ thống vụ việc"},{id:"news_view",name:"Xem tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể xem danh s\xe1ch tin tức v\xe0 b\xe0i viết"},{id:"news_create",name:"Tạo tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể tạo tin tức v\xe0 b\xe0i viết mới"},{id:"news_edit",name:"Chỉnh sửa tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể chỉnh sửa tin tức v\xe0 b\xe0i viết"},{id:"news_delete",name:"X\xf3a tin tức",category:"Quản l\xfd tin tức",description:"C\xf3 thể x\xf3a tin tức v\xe0 b\xe0i viết"},{id:"system_settings_view",name:"Xem c\xe0i đặt hệ thống",category:"Quản l\xfd hệ thống",description:"C\xf3 thể xem c\xe1c c\xe0i đặt hệ thống"},{id:"system_settings_edit",name:"Chỉnh sửa c\xe0i đặt hệ thống",category:"Quản l\xfd hệ thống",description:"C\xf3 thể chỉnh sửa c\xe1c c\xe0i đặt hệ thống"},{id:"analytics_view",name:"Xem ph\xe2n t\xedch",category:"Quản l\xfd hệ thống",description:"C\xf3 thể xem c\xe1c b\xe1o c\xe1o ph\xe2n t\xedch hệ thống"}].forEach(e=>{y.set(e.id,e)})},21379:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(12115),i=n(38637),a=n.n(i);function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=(0,r.forwardRef)(function(e,t){var n=e.color,i=e.size,a=void 0===i?24:i,l=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return r.createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),r.createElement("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),r.createElement("circle",{cx:"9",cy:"7",r:"4"}),r.createElement("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),r.createElement("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Users";let c=l},52814:(e,t,n)=>{"use strict";n.d(t,{Ex:()=>i,eG:()=>a});var r=n(95155);n(12115);let i=e=>{let{children:t,variant:n="default",size:i="md",className:a="",dot:s=!1}=e;return(0,r.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[n],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[i],"\n        ").concat(a,"\n      "),children:[s&&(0,r.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[n])}),t]})},a=e=>{let{role:t,className:n=""}=e,a={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}}[t];return(0,r.jsx)(i,{variant:a.variant,className:n,children:a.label})}},68661:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(12115),i=n(38637),a=n.n(i);function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=(0,r.forwardRef)(function(e,t){var n=e.color,i=e.size,a=void 0===i?24:i,l=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return r.createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),r.createElement("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),r.createElement("line",{x1:"5",y1:"12",x2:"19",y2:"12"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Plus";let c=l},84091:(e,t,n)=>{Promise.resolve().then(n.bind(n,14638))},95512:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(12115),i=n(38637),a=n.n(i);function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=(0,r.forwardRef)(function(e,t){var n=e.color,i=e.size,a=void 0===i?24:i,l=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,["color","size"]);return r.createElement("svg",s({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),r.createElement("circle",{cx:"12",cy:"12",r:"3"}),r.createElement("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Settings";let c=l}},e=>{e.O(0,[9268,3235,8543,7617,8441,5964,7358],()=>e(e.s=84091)),_N_E=e.O()}]);