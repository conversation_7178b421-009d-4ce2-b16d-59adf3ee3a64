(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5740],{8509:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});var r=t(12115),a=t(38637),s=t.n(a);function l(){return(l=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}var i=(0,r.forwardRef)(function(e,n){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,n){if(null==e)return{};var t,r,a=function(e,n){if(null==e)return{};var t,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)t=s[r],n.indexOf(t)>=0||(a[t]=e[t]);return a}(e,n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)t=s[r],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return r.createElement("svg",l({ref:n,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),r.createElement("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),r.createElement("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Edit";let o=i},18579:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var r=t(95155);function a(e){let{currentPage:n,totalPages:t,onPageChange:a}=e;return(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>a(Math.max(n-1,1)),disabled:1===n,children:"Previous"}),(0,r.jsxs)("span",{children:["Page ",n," / ",t]}),(0,r.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>a(Math.min(n+1,t)),disabled:n===t,children:"Next"})]})}t(12115)},21379:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});var r=t(12115),a=t(38637),s=t.n(a);function l(){return(l=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}var i=(0,r.forwardRef)(function(e,n){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,n){if(null==e)return{};var t,r,a=function(e,n){if(null==e)return{};var t,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)t=s[r],n.indexOf(t)>=0||(a[t]=e[t]);return a}(e,n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)t=s[r],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return r.createElement("svg",l({ref:n,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),r.createElement("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),r.createElement("circle",{cx:"9",cy:"7",r:"4"}),r.createElement("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),r.createElement("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Users";let o=i},25653:(e,n,t)=>{"use strict";t.d(n,{default:()=>x});var r=t(95155),a=t(12115),s=t(36268),l=t(11032),i=t(3136),o=t(38543),c=t(35695),d=t(18579),h=t(52814),u=t(21379),g=t(8509),p=t(46114),m=t(68661);function x(){let[e,n]=(0,a.useState)([]),[t,x]=(0,a.useState)(!0),[b,f]=(0,a.useState)(0),[y,v]=(0,a.useState)(1),[j,w]=(0,a.useState)(""),O=(0,c.useRouter)(),N=[{accessorKey:"name",header:"T\xean ph\xf2ng ban",cell:e=>{let{row:n}=e;return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:n.original.name}),n.original.description&&(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:n.original.description})]})}},{accessorKey:"manager",header:"Quản l\xfd",cell:e=>{let{row:n}=e;return(0,r.jsx)("div",{children:n.original.manager?(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:n.original.manager.username}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:n.original.manager.email})]}):(0,r.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 quản l\xfd"})})}},{accessorKey:"memberCount",header:"Số th\xe0nh vi\xean",cell:e=>{let{row:n}=e;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{size:16,className:"text-gray-500"}),(0,r.jsx)("span",{className:"font-medium",children:n.original.memberCount})]})}},{accessorKey:"defaultPermissions",header:"Quyền mặc định",cell:e=>{let{row:n}=e;return(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:n.original.defaultPermissions.length>0?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(h.Ex,{variant:"secondary",className:"text-xs",children:[n.original.defaultPermissions.length," quyền"]})}):(0,r.jsx)("span",{className:"text-gray-400 italic text-sm",children:"Kh\xf4ng c\xf3 quyền"})})}},{accessorKey:"isActive",header:"Trạng th\xe1i",cell:e=>{let{row:n}=e;return(0,r.jsx)(h.Ex,{variant:n.original.isActive?"success":"danger",children:n.original.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})}},{id:"actions",header:"Thao t\xe1c",cell:e=>{let{row:n}=e;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>O.push("/dashboard/departments/".concat(n.original._id)),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,r.jsx)(u.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>O.push("/dashboard/departments/".concat(n.original._id,"/edit")),className:"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,r.jsx)(g.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>C(n.original._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,r.jsx)(p.A,{size:16})})]})}}],k=(0,s.N4)({data:e,columns:N,getCoreRowModel:(0,l.HT)()}),E=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{x(!0);let r=localStorage.getItem("sessionToken")||"",a=await i.A.getDepartments({page:e,perPage:10,search:t},r);a.payload.success?(n(a.payload.departments),f(a.payload.total)):o.oR.error("Kh\xf4ng thể tải danh s\xe1ch ph\xf2ng ban")}catch(e){console.error("Error fetching departments:",e),o.oR.error("Lỗi khi tải danh s\xe1ch ph\xf2ng ban")}finally{x(!1)}},C=async n=>{let t=e.find(e=>e._id===n),r=(null==t?void 0:t.memberCount)||0,a="Bạn c\xf3 chắc chắn muốn x\xf3a ph\xf2ng ban n\xe0y?";if(r>0&&(a="⚠️ CẢNH B\xc1O: Ph\xf2ng ban n\xe0y c\xf3 ".concat(r," th\xe0nh vi\xean!\n\n")+"Khi x\xf3a ph\xf2ng ban:\n"+"• Tất cả ".concat(r," th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban\n")+"• Họ sẽ trở th\xe0nh người d\xf9ng thường (kh\xf4ng thuộc ph\xf2ng ban n\xe0o)\n• C\xe1c quyền li\xean quan đến ph\xf2ng ban sẽ bị mất\n\nBạn c\xf3 chắc chắn muốn tiếp tục?"),confirm(a))try{let e=localStorage.getItem("sessionToken")||"",t=await i.A.deleteDepartment(n,e);t.payload.success?(t.payload.affectedMembers>0?o.oR.success("X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng! ".concat(t.payload.affectedMembers," th\xe0nh vi\xean đ\xe3 được chuyển về trạng th\xe1i kh\xf4ng thuộc ph\xf2ng ban."),{autoClose:5e3}):o.oR.success("X\xf3a ph\xf2ng ban th\xe0nh c\xf4ng"),E(y,j)):o.oR.error(t.payload.message||"Kh\xf4ng thể x\xf3a ph\xf2ng ban")}catch(e){console.error("Error deleting department:",e),o.oR.error("Lỗi khi x\xf3a ph\xf2ng ban")}};return((0,a.useEffect)(()=>{E(y,j)},[y]),t)?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tổng cộng ",b," ph\xf2ng ban"]})]}),(0,r.jsxs)("button",{onClick:()=>O.push("/dashboard/departments/add"),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(m.A,{size:16}),"Th\xeam ph\xf2ng ban"]})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),v(1),E(1,j)},className:"flex gap-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"T\xecm kiếm theo t\xean, m\xe3 hoặc m\xf4 tả...",value:j,onChange:e=>w(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"T\xecm kiếm"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:k.getHeaderGroups().map(e=>(0,r.jsx)("tr",{children:e.headers.map(e=>(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.isPlaceholder?null:(0,s.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:k.getRowModel().rows.map(e=>(0,r.jsx)("tr",{className:"hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),0===e.length&&(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Kh\xf4ng c\xf3 ph\xf2ng ban n\xe0o"})]}),b>10&&(0,r.jsx)(d.A,{currentPage:y,totalPages:Math.ceil(b/10),onPageChange:v})]})}},46114:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});var r=t(12115),a=t(38637),s=t.n(a);function l(){return(l=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}var i=(0,r.forwardRef)(function(e,n){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,n){if(null==e)return{};var t,r,a=function(e,n){if(null==e)return{};var t,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)t=s[r],n.indexOf(t)>=0||(a[t]=e[t]);return a}(e,n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)t=s[r],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return r.createElement("svg",l({ref:n,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),r.createElement("polyline",{points:"3 6 5 6 21 6"}),r.createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),r.createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),r.createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Trash2";let o=i},52814:(e,n,t)=>{"use strict";t.d(n,{Ex:()=>a,eG:()=>s});var r=t(95155);t(12115);let a=e=>{let{children:n,variant:t="default",size:a="md",className:s="",dot:l=!1}=e;return(0,r.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[t],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[a],"\n        ").concat(s,"\n      "),children:[l&&(0,r.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[t])}),n]})},s=e=>{let{role:n,className:t=""}=e,s={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}}[n];return(0,r.jsx)(a,{variant:s.variant,className:t,children:s.label})}},68661:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});var r=t(12115),a=t(38637),s=t.n(a);function l(){return(l=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}var i=(0,r.forwardRef)(function(e,n){var t=e.color,a=e.size,s=void 0===a?24:a,i=function(e,n){if(null==e)return{};var t,r,a=function(e,n){if(null==e)return{};var t,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)t=s[r],n.indexOf(t)>=0||(a[t]=e[t]);return a}(e,n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)t=s[r],!(n.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,["color","size"]);return r.createElement("svg",l({ref:n,xmlns:"http://www.w3.org/2000/svg",width:s,height:s,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),r.createElement("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),r.createElement("line",{x1:"5",y1:"12",x2:"19",y2:"12"}))});i.propTypes={color:s().string,size:s().oneOfType([s().string,s().number])},i.displayName="Plus";let o=i},69489:(e,n,t)=>{Promise.resolve().then(t.bind(t,87708)),Promise.resolve().then(t.bind(t,25653))}},e=>{e.O(0,[9268,3235,8543,6268,7617,8441,5964,7358],()=>e(e.s=69489)),_N_E=e.O()}]);