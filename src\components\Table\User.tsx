"use client";
import { useEffect, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
} from "@tanstack/react-table";
import userApiRequest from "@/apiRequests/user";
import adminApiRequest from "@/apiRequests/admin";
import { toast } from "react-toastify";
import envConfig from "@/config";
import { useRouter } from "next/navigation";
import Pagination from "@/components/Widget/Pagination";
import { RoleBadge } from "@/components/ui/Badge";

interface UserFormValues {
  _id: string;
  username: string;
  email: string;
  phonenumber?: string;
  rule: string;
  departmentRole?: string;
  private: boolean;
  createdAt: string;
  department?: {
    _id: string;
    name: string;
    code: string;
  };
}

export default function UserTable() {
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState(1);
  const perPage = 20;
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);

  const fetchUsers = async () => {
    const data = { page, perPage };
    setLoading(true);
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const resUsers = await userApiRequest.fetchUsers(data, sessionToken);
      console.log("Fetch users response:", resUsers);
      
      if (resUsers && resUsers.payload) {
        const { total, users: fetchedUsers } = resUsers.payload;
        setUsers(fetchedUsers || []); // Ensure it's always an array
        setTotalPages(Math.ceil(total / perPage));
      } else {
        console.warn("No payload in users response");
        setUsers([]); // Set to empty array if no data
      }
    } catch (error: any) {
      console.error("Error fetching users:", error);
      setUsers([]); // Set to empty array on error
      toast.error(
        "An error occurred while fetching users. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page]);
  const handleDelete = async (data: UserFormValues, index: number) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.deleteUser(data, sessionToken);
      
      if (result.payload.success) {
        // Create a new array without mutating the original `categories` state
        const updatedUser = [...users];
        updatedUser.splice(index, 1); // Remove the category at the specified index
        
        setUsers(updatedUser); // Update the state with the new array
        toast.success("Delete successful!");
      } else {
        console.error("Error deleting user:", result.payload);
        toast.error("Error deleting user. Please try again.");
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An error occurred during deletion. Please try again.");
    }
  };

  const handleTogglePrivate = async (user: any) => {
    const action = user.private ? "mở khoá" : "khoá";
    if (confirm(`Bạn có chắc chắn muốn ${action} tài khoản ${user.username}?`)) {
      try {
        const sessionToken = localStorage.getItem("sessionToken") || "";
        const result = await adminApiRequest.toggleUserPrivate(user._id, sessionToken);
        if (result.payload.success) {
          toast.success(`${action.charAt(0).toUpperCase() + action.slice(1)} tài khoản thành công`);
          fetchUsers(); // Refresh the user list
        } else {
          toast.error(`Không thể ${action} tài khoản`);
        }
      } catch (error) {
        console.error("Error toggling user private status:", error);
        toast.error(`Có lỗi xảy ra khi ${action} tài khoản`);
      }
    }
  };

  const columns = [
    { accessorKey: "username", header: "Tên" },
    { accessorKey: "phonenumber", header: "Số điện thoại" },
    { accessorKey: "email", header: "Email" },
    {
      accessorKey: "department",
      header: "Phòng ban",
      cell: ({ row }: any) => (
        <div>
          {row.original.department ? (
            <div className="font-medium text-sm">{row.original.department.name}</div>
          ) : (
            <span className="text-gray-400 italic text-sm">Chưa có phòng ban</span>
          )}
        </div>
      )
    },
    {
      accessorKey: "rule",
      header: "Chức vụ",
      cell: ({ row }: any) => (
        <div>
          <RoleBadge role={row.original.rule} />
          {row.original.departmentRole && row.original.departmentRole !== 'member' && (
            <div className="mt-1">
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                {row.original.departmentRole === 'manager' ? 'Quản lý' : row.original.departmentRole}
              </span>
            </div>
          )}
        </div>
      )
    },
    {
      accessorKey: "private",
      header: "Tình Trạng",
      cell: ({ row }: any) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.original.private
            ? 'bg-red-100 text-red-800'
            : 'bg-green-100 text-green-800'
        }`}>
          {row.original.private ? 'Đã khoá' : 'Hoạt động'}
        </span>
      )
    },
    {
      accessorKey: "createdAt",
      header: "Ngày đăng",
      cell: ({ row }: any) =>
        new Date(row.original.createdAt).toLocaleDateString(),
    },
    {
      header: "Hành động",
      cell: ({ row, rowIndex }: any) => {
        const router = useRouter();
        const handleEdit = () => {
          router.push(`/dashboard/user/${row.original._id}`);
        };

        return (
          <div className="flex items-center gap-2">
            <button
              onClick={handleEdit}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Chỉnh sửa
            </button>
            <button
              onClick={() => handleTogglePrivate(row.original)}
              className={`px-3 py-1 text-sm rounded-md ${
                row.original.private
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-yellow-500 hover:bg-yellow-600 text-white'
              }`}
            >
              {row.original.private ? 'Mở khoá' : 'Khoá'}
            </button>
            <button onClick={() => handleDelete(row.original, row.index)} className="px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600">
              Xóa
            </button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: users || [], // Ensure data is always an array
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (loading) {
    return (
      <div className="w-full p-4 flex items-center justify-center">
        <div className="text-gray-600">Đang tải dữ liệu...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      {users.length === 0 ? (
        <div className="text-center text-gray-600 py-8">
          Không có dữ liệu người dùng
        </div>
      ) : (
        <>
          <table className="table-auto w-full border-collapse border border-gray-300 bg-white">
            <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id} className="bg-gray-200">
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="border border-gray-300 px-4 py-2 text-gray-800 font-semibold"
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr key={row.id} className="border border-gray-300 hover:bg-gray-50">
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id} className="border border-gray-300 px-4 py-2 text-gray-900">
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      <Pagination
        currentPage={page}
        totalPages={totalPages}
        onPageChange={(newPage) => setPage(newPage)}
      />
        </>
      )}
    </div>
  );
}
