// Permission mapping utility
export interface Permission {
  id: string;
  name: string;
  category: string;
  description: string;
}

// All available permissions with Vietnamese names
export const AVAILABLE_PERMISSIONS: Permission[] = [
  {
    id: 'user_view',
    name: '<PERSON>em danh sách người dùng',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể xem danh sách và thông tin người dùng'
  },
  {
    id: 'user_add',
    name: 'Thêm người dùng mới',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể tạo tài khoản người dùng mới'
  },
  {
    id: 'user_edit',
    name: 'Chỉnh sửa người dùng',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể chỉnh sửa thông tin người dùng'
  },
  {
    id: 'user_delete',
    name: '<PERSON><PERSON><PERSON> người dùng',
    category: 'Quản lý người dùng',
    description: '<PERSON><PERSON> thể xóa tài khoản người dùng'
  },
  {
    id: 'user_import_csv',
    name: '<PERSON><PERSON><PERSON><PERSON> thành viên từ CSV',
    category: 'Quản lý thành viên',
    description: 'Có thể nhập danh sách thành viên từ file CSV'
  },
  {
    id: 'file_view',
    name: 'Xem danh sách file',
    category: 'Quản lý file',
    description: 'Có thể xem danh sách file và tài liệu'
  },
  {
    id: 'file_upload',
    name: 'Tải lên file',
    category: 'Quản lý file',
    description: 'Có thể tải lên file và tài liệu mới'
  },
  {
    id: 'file_delete',
    name: 'Xóa file',
    category: 'Quản lý file',
    description: 'Có thể xóa file và tài liệu'
  },
  
  // Court Case Management permissions
  {
    id: 'court_case_view',
    name: 'Xem danh sách vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem danh sách và thông tin vụ việc tòa án'
  },
  {
    id: 'court_case_create',
    name: 'Tạo vụ việc mới',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể tạo vụ việc tòa án mới'
  },
  {
    id: 'court_case_edit',
    name: 'Chỉnh sửa vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể chỉnh sửa thông tin vụ việc tòa án'
  },
  {
    id: 'court_case_delete',
    name: 'Xóa vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xóa vụ việc tòa án'
  },
  {
    id: 'court_case_export',
    name: 'Xuất dữ liệu vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xuất danh sách vụ việc ra file Excel/CSV'
  },
  {
    id: 'court_case_import',
    name: 'Nhập dữ liệu vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể nhập danh sách vụ việc từ file Excel/CSV'
  },
  {
    id: 'court_case_stats_view',
    name: 'Xem thống kê vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem báo cáo và thống kê vụ việc tòa án'
  },
  {
    id: 'court_case_detailed_stats_view',
    name: 'Xem thống kê chi tiết',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể xem thống kê chi tiết và nâng cao'
  },
  {
    id: 'court_case_print',
    name: 'In vụ việc',
    category: 'Quản lý vụ việc tòa án',
    description: 'Có thể in thông tin vụ việc tòa án'
  },
  {
    id: 'court_case_user_profile_view',
    name: 'Xem hồ sơ người dùng',
    category: 'Quản lý vụ việc tòa án',
    description: 'Xem thông tin hồ sơ người dùng trong hệ thống vụ việc'
  },
  {
    id: 'court_case_user_profile_edit',
    name: 'Chỉnh sửa hồ sơ người dùng',
    category: 'Quản lý vụ việc tòa án',
    description: 'Chỉnh sửa thông tin hồ sơ người dùng trong hệ thống vụ việc'
  },
  
  // News Management permissions
  {
    id: 'news_view',
    name: 'Xem tin tức',
    category: 'Quản lý tin tức',
    description: 'Có thể xem danh sách tin tức và bài viết'
  },
  {
    id: 'news_create',
    name: 'Tạo tin tức',
    category: 'Quản lý tin tức',
    description: 'Có thể tạo tin tức và bài viết mới'
  },
  {
    id: 'news_edit',
    name: 'Chỉnh sửa tin tức',
    category: 'Quản lý tin tức',
    description: 'Có thể chỉnh sửa tin tức và bài viết'
  },
  {
    id: 'news_delete',
    name: 'Xóa tin tức',
    category: 'Quản lý tin tức',
    description: 'Có thể xóa tin tức và bài viết'
  },
  
  // System permissions
  {
    id: 'system_settings_view',
    name: 'Xem cài đặt hệ thống',
    category: 'Quản lý hệ thống',
    description: 'Có thể xem các cài đặt hệ thống'
  },
  {
    id: 'system_settings_edit',
    name: 'Chỉnh sửa cài đặt hệ thống',
    category: 'Quản lý hệ thống',
    description: 'Có thể chỉnh sửa các cài đặt hệ thống'
  },
  {
    id: 'analytics_view',
    name: 'Xem phân tích',
    category: 'Quản lý hệ thống',
    description: 'Có thể xem các báo cáo phân tích hệ thống'
  },
];

// Create a mapping from permission ID to permission object
const permissionMap = new Map<string, Permission>();
AVAILABLE_PERMISSIONS.forEach(permission => {
  permissionMap.set(permission.id, permission);
});

/**
 * Get the Vietnamese display name for a permission ID
 * @param permissionId - The technical permission ID (e.g., 'user_view')
 * @returns The Vietnamese display name or the original ID if not found
 */
export function getPermissionDisplayName(permissionId: string): string {
  const permission = permissionMap.get(permissionId);
  return permission ? permission.name : permissionId;
}

/**
 * Get the full permission object by ID
 * @param permissionId - The technical permission ID
 * @returns The permission object or undefined if not found
 */
export function getPermission(permissionId: string): Permission | undefined {
  return permissionMap.get(permissionId);
}

/**
 * Get permissions grouped by category
 * @returns Permissions grouped by their category
 */
export function getPermissionsByCategory(): Record<string, Permission[]> {
  const grouped: Record<string, Permission[]> = {};
  
  AVAILABLE_PERMISSIONS.forEach(permission => {
    if (!grouped[permission.category]) {
      grouped[permission.category] = [];
    }
    grouped[permission.category].push(permission);
  });
  
  return grouped;
}

/**
 * Get display names for multiple permission IDs
 * @param permissionIds - Array of permission IDs
 * @returns Array of Vietnamese display names
 */
export function getPermissionDisplayNames(permissionIds: string[]): string[] {
  return permissionIds.map(id => getPermissionDisplayName(id));
}
