(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8039],{30590:(e,a,l)=>{Promise.resolve().then(l.bind(l,51901))},51901:(e,a,l)=>{"use strict";l.r(a),l.d(a,{default:()=>t});var s=l(95155),r=l(12115);function t(e){let{error:a,reset:l}=e;return(0,r.useEffect)(()=>{console.error(a)},[a]),(0,s.jsx)("div",{className:"bg-gray-100 py-16 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-lg max-w-md w-full",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"404 - Page Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"The page you are looking for might have been removed, had its name changed or is temporarily unavailable."}),(0,s.jsx)("a",{href:"/",className:"inline-block py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold",children:"Go back to homepage"})]})})}}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=30590)),_N_E=e.O()}]);