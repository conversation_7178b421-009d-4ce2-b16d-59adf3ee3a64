(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6474],{60123:(e,a,n)=>{Promise.resolve().then(n.bind(n,97684))},97684:(e,a,n)=>{"use strict";n.r(a),n.d(a,{default:()=>r});var l=n(95155),t=n(12115),s=n(35695),c=n(74660),i=n(71592);function r(){return(0,l.jsx)(t.Suspense,{fallback:(0,l.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,l.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:"Loading..."})}),children:(0,l.jsx)(d,{})})}function d(){let e=(0,s.useSearchParams)().get("id")||"",[a,n]=(0,t.useState)(""),[r,d]=(0,t.useState)("");return((0,t.useEffect)(()=>{if(!e)return void d("Invalid verification code.");(async()=>{try{var a,l;let t=await i.A.checkCode(e),s=null!=(l=null==t||null==(a=t.payload)?void 0:a.userId)?l:"";n(s)}catch(e){d("Failed to load verification. Please try again later.")}})()},[e]),r)?(0,l.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,l.jsx)("p",{children:r})}):a?(0,l.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,l.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,l.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,l.jsx)("h1",{className:"text-2xl text-center mb-4",children:"X\xe1c nhận bước 2"}),(0,l.jsx)("span",{className:"text-center block mb-4",children:"Bạn cần kiểm tra Email đ\xe3 khai b\xe1o nhận m\xe3 x\xe1c thực lớp 2, lấy m\xe3 v\xe0 nhập v\xe0o \xf4 dưới đ\xe2y."}),(0,l.jsx)(c.A,{userId:a,typeVerify:"authmail"})]})})}):(0,l.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,l.jsx)("p",{children:"Loading user information..."})})}}},e=>{e.O(0,[9268,3235,8543,2182,716,3085,8441,5964,7358],()=>e(e.s=60123)),_N_E=e.O()}]);