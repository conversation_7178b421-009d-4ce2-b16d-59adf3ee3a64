(()=>{var a={};a.id=2828,a.ids=[2828],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4875:(a,b,c)=>{Promise.resolve().then(c.bind(c,80950))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28690:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>M});var d=c(60687),e=c(43210),f=c(93853),g=c(9113);let h={getCourtCases:(a={})=>{let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,c.toString())});let c=b.toString(),d=c?`/api/court-cases?${c}`:"/api/court-cases";return g.Ay.get(d)},createCourtCase:a=>g.Ay.post("/api/court-cases",a),updateCourtCase:(a,b)=>g.Ay.put(`/api/court-cases/${a}`,b),deleteCourtCase:a=>g.Ay.delete(`/api/court-cases/${a}`),getCourtCaseStats:()=>g.Ay.get("/api/court-cases/stats"),bulkDeleteCourtCases:a=>g.Ay.post("/api/court-cases/bulk-delete",{ids:a}),downloadTemplate:async()=>{let a=await fetch("/api/court-cases/template",{method:"GET",headers:{}});if(!a.ok)throw Error("Failed to download template");let b=await a.blob(),c=window.URL.createObjectURL(b),d=document.createElement("a");return d.href=c,d.download="mau-import-vu-viec-toa-an.xlsx",document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(c),{success:!0}},exportCourtCases:async(a={})=>{let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,c.toString())});let c=b.toString(),d=c?`/api/court-cases/export?${c}`:"/api/court-cases/export",e=await fetch(d,{method:"GET",headers:{}});if(!e.ok)throw Error("Failed to export data");return e.arrayBuffer()},previewImport:a=>{let b=new FormData;return b.append("file",a),g.Ay.post("/api/court-cases/preview-import",b)},importCourtCases:a=>{let b=new FormData;return b.append("file",a),g.Ay.post("/api/court-cases/import",b)},getDetailedStats:(a={})=>{let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,c.toString())});let c=b.toString(),d=c?`/api/court-cases/detailed-stats?${c}`:"/api/court-cases/detailed-stats";return g.Ay.get(d)}};var i=c(66800),j=c(55109);let k=({cases:a,onCaseSelect:b,onCaseEdit:c,onCaseDelete:g,onBulkAction:h,onSort:k,currentSort:l,loading:m=!1})=>{let{hasPermission:n}=(0,j.S)(),[o,p]=(0,e.useState)([]),[q,r]=(0,e.useState)(!1);return m?(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"h-5 w-48 bg-gray-200 rounded animate-pulse mb-2"}),(0,d.jsx)("div",{className:"h-4 w-32 bg-gray-200 rounded animate-pulse"})]})]})})}),(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:[...Array(8)].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"})]})]},b))})})]}):(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:[(0,d.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch vụ việc t\xf2a \xe1n"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Tổng số: ",(0,d.jsx)("span",{className:"font-medium text-blue-600",children:a.length})," vụ việc"]})]}),(0,d.jsx)("div",{className:"hidden md:flex items-center gap-2 text-sm text-gray-500",children:(0,d.jsxs)("span",{className:"px-2 py-1 bg-white rounded-md border",children:[a.length," kết quả"]})})]})}),o.length>0&&n("court_case_delete")&&(0,d.jsx)("div",{className:"bg-blue-50 px-6 py-3 border-b border-blue-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsxs)("span",{className:"text-sm text-blue-700 font-medium",children:["Đ\xe3 chọn ",o.length," vụ việc"]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>{if(0===o.length)return void f.oR.warning("Vui l\xf2ng chọn \xedt nhất một vụ việc để x\xf3a");confirm(`Bạn c\xf3 chắc chắn muốn x\xf3a ${o.length} vụ việc đ\xe3 chọn?`)&&(h(o,"delete"),p([]),r(!1))},className:"flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium",children:"X\xf3a đ\xe3 chọn"}),(0,d.jsx)("button",{onClick:()=>{p([]),r(!1)},className:"flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors",children:"Bỏ chọn"})]})]})}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",style:{minWidth:"3000px"},children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[n("court_case_delete")&&(0,d.jsx)("th",{className:"px-4 py-4 text-left w-12",children:(0,d.jsx)("input",{type:"checkbox",checked:q,onChange:b=>(b=>{r(b),b?p(a.map(a=>a._id)):p([])})(b.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-16 bg-gray-100",children:"STT"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"SỐ VĂN THƯ"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-36",children:"NG\xc0Y NHẬN VĂN THƯ"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28",children:"LOẠI \xc1N"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"SỐ THỤ L\xdd"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"NG\xc0Y THỤ L\xdd"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"TAND"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"SỐ BẢN \xc1N"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-36",children:"NG\xc0Y BAN H\xc0NH BẢN \xc1N"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:"BỊ C\xc1O/NĐ/NKK"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:"TỘI DANH/BĐ/NBK"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:"TỘI DANH/QHPL"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"H\xccNH THỨC"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28",children:"THỦ TỤC"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"THẨM PH\xc1N"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:"TRƯỞNG/PH\xd3 PH\xd2NG KTNV/THẨM TRA VI\xcaN"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"GHI CH\xda"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"GHI CH\xda KQ"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"TRẠNG TH\xc1I"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:k?(0,d.jsx)(({field:a,children:b})=>{let c=l?.sortBy===a,e=c&&l?.sortOrder==="asc",f=c&&l?.sortOrder==="desc";return(0,d.jsxs)("button",{onClick:()=>(a=>{if(!k)return;let b="asc";l?.sortBy===a&&(b="asc"===l.sortOrder?"desc":"asc"),k(a,b)})(a),className:"flex items-center gap-1 hover:text-gray-700 transition-colors",children:[b,(0,d.jsxs)("span",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:`text-xs leading-none ${e?"text-blue-600":"text-gray-400"}`,children:"▲"}),(0,d.jsx)("span",{className:`text-xs leading-none ${f?"text-blue-600":"text-gray-400"}`,children:"▼"})]})]})},{field:"soNgayConLai",children:"THỜI HẠN 90 NG\xc0Y VỚI NG\xc0Y NHẬN VĂN THƯ"}):"THỜI HẠN 90 NG\xc0Y VỚI NG\xc0Y NHẬN VĂN THƯ"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44",children:"THỜI GIAN C\xd2N LẠI CỦA THỜI HẠN 90 NG\xc0Y NHẬN VĂN THƯ"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28",children:"Thao t\xe1c"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:0===a.length?(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:n("court_case_delete")?23:22,className:"px-4 py-12 text-center",children:(0,d.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-3",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Kh\xf4ng c\xf3 vụ việc n\xe0o"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Thử điều chỉnh bộ lọc hoặc th\xeam vụ việc mới"})]})]})})}):a.map((a,e)=>(0,d.jsxs)("tr",{className:"hover:bg-blue-50 transition-colors duration-150 group",children:[n("court_case_delete")&&(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("input",{type:"checkbox",checked:o.includes(a._id),onChange:b=>((a,b)=>{b?p(b=>[...b,a]):(p(b=>b.filter(b=>b!==a)),r(!1))})(a._id,b.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,d.jsx)("td",{className:"px-4 py-4 text-center",children:(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.stt})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.soVanThu,children:a.soVanThu||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 font-medium",children:(0,i.Yq)(a.ngayNhanVanThu)})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(a=>{if(!a)return"\uD83D\uDCC4";let b=a.toLowerCase();return b.includes("h\xecnh sự")?"⚖️":b.includes("d\xe2n sự")?"\uD83C\uDFE0":b.includes("h\xe0nh ch\xednh")?"\uD83C\uDFDB️":b.includes("kinh tế")?"\uD83D\uDCBC":b.includes("lao động")?"\uD83D\uDC77":"\uD83D\uDCCB"})(a.loaiAn)}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:a.loaiAn})]})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 font-medium truncate",title:a.soThuLy,children:a.soThuLy||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 font-medium",children:(0,i.Yq)(a.ngayThuLy)})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.tand,children:a.tand||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 font-medium truncate",title:a.soBanAn,children:a.soBanAn||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 font-medium",children:(0,i.Yq)(a.ngayBanHanh)})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.biCaoNguoiKhieuKien,children:a.biCaoNguoiKhieuKien||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.toiDanhNoiDung,children:a.toiDanhNoiDung||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.quanHePhatLuat,children:a.quanHePhatLuat||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.hinhThucXuLy,children:a.hinhThucXuLy||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700",children:a.thuTucApDung||"Chưa c\xf3"})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.thamPhanPhuTrach,children:a.thamPhanPhuTrach||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.truongPhoPhongKTNV,children:a.truongPhoPhongKTNV||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.ghiChu,children:a.ghiChu||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 truncate",title:a.ghiChuKetQua,children:a.ghiChuKetQua||(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3"})})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${(a=>{switch(a){case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800";case"Chưa giải quyết":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.trangThaiGiaiQuyet)}`,children:a.trangThaiGiaiQuyet})}),(0,d.jsx)("td",{className:"px-4 py-4",children:a.ngayNhanVanThu&&a.thoiHan90NgayFormatted?(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.thoiHan90NgayFormatted}):(0,d.jsx)("span",{className:"text-xs text-gray-400 italic",children:"Chưa c\xf3 ng\xe0y nhận văn thư"})}),(0,d.jsx)("td",{className:"px-4 py-4",children:a.ngayNhanVanThu&&a.trangThaiThoiHan?(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${(a=>{switch(a){case"Qu\xe1 hạn":case"Gần hết hạn":return"bg-red-100 text-red-800 border border-red-300";case"Sắp hết hạn":return"bg-yellow-100 text-yellow-800 border border-yellow-300";case"C\xf2n thời gian":return"bg-green-100 text-green-800 border border-green-300";default:return"bg-gray-100 text-gray-800 border border-gray-300"}})(a.trangThaiThoiHan)}`,children:((a,b)=>void 0===a||void 0===b?"":a<0?`Qu\xe1 hạn ${Math.abs(a)} ng\xe0y`:0===a?"Hết hạn h\xf4m nay":`C\xf2n ${a} ng\xe0y`)(a.soNgayConLai,a.trangThaiThoiHan)}):(0,d.jsx)("span",{className:"text-xs text-gray-400 italic",children:"Chưa c\xf3"})}),(0,d.jsx)("td",{className:"px-4 py-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[n("court_case_view")&&(0,d.jsx)("button",{onClick:()=>b(a),className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-150",title:"Xem chi tiết",children:(0,d.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),n("court_case_edit")&&(0,d.jsx)("button",{onClick:()=>c(a),className:"p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-150",title:"Chỉnh sửa",children:(0,d.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),n("court_case_delete")&&(0,d.jsx)("button",{onClick:()=>g(a._id),className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-150",title:"X\xf3a",children:(0,d.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},a._id))})]})})]})};var l=c(27605),m=c(558),n=c(49445);let o=n.k5(["Chưa giải quyết","Đang giải quyết","Đ\xe3 giải quyết"]),p=n.Ik({soVanThu:n.Yj().max(100).optional(),ngayNhanVanThu:n.Yj().optional(),loaiAn:n.Yj().max(100).optional(),soThuLy:n.Yj().max(100).optional(),ngayThuLy:n.Yj().optional(),tand:n.Yj().max(200).optional(),soBanAn:n.Yj().max(100).optional(),ngayBanHanh:n.Yj().optional(),biCaoNguoiKhieuKien:n.Yj().max(500).optional(),toiDanhNoiDung:n.Yj().max(1e3).optional(),quanHePhatLuat:n.Yj().max(500).optional(),hinhThucXuLy:n.Yj().max(200).optional(),thuTucApDung:n.Yj().max(100).optional(),thamPhanPhuTrach:n.Yj().max(200).optional(),truongPhoPhongKTNV:n.Yj().max(200).optional(),ghiChu:n.Yj().max(1e3).optional(),ghiChuKetQua:n.Yj().max(1e3).optional(),trangThaiGiaiQuyet:o.default("Chưa giải quyết"),thoiHan90NgayVoiNgayNhanVanThu:n.Yj().optional(),thoiGianConLaiCuaThoiHan90NgayNhanVanThu:n.ai().optional()});p.partial(),n.Ik({page:n.ai().int().positive().default(1),limit:n.ai().int().positive().max(100).default(20),search:n.Yj().optional(),loaiAn:n.Yj().optional(),trangThaiGiaiQuyet:o.optional(),thuTucApDung:n.Yj().optional(),fromDate:n.Yj().optional(),toDate:n.Yj().optional(),sortBy:n.Yj().default("createdAt"),sortOrder:n.k5(["asc","desc"]).default("desc")});let q=p.extend({_id:n.Yj(),stt:n.ai(),createdAt:n.Yj(),updatedAt:n.Yj(),createdBy:n.Ik({_id:n.Yj(),username:n.Yj(),email:n.Yj().optional()}).optional(),updatedBy:n.Ik({_id:n.Yj(),username:n.Yj(),email:n.Yj().optional()}).optional(),ngayNhanVanThuFormatted:n.Yj().optional(),ngayThuLyFormatted:n.Yj().optional(),ngayBanHanhFormatted:n.Yj().optional(),thoiHan90Ngay:n.Yj().optional(),thoiHan90NgayFormatted:n.Yj().optional(),soNgayConLai:n.ai().optional(),trangThaiThoiHan:n.k5(["Qu\xe1 hạn","Sắp hết hạn","Gần hết hạn","C\xf2n thời gian"]).optional()});n.Ik({success:n.zM(),cases:n.YO(q),pagination:n.Ik({currentPage:n.ai(),totalPages:n.ai(),totalItems:n.ai(),itemsPerPage:n.ai(),hasNextPage:n.zM(),hasPrevPage:n.zM()})}),n.Ik({success:n.zM(),stats:n.Ik({total:n.ai(),byStatus:n.YO(n.Ik({_id:n.Yj(),count:n.ai()})),byType:n.YO(n.Ik({_id:n.Yj(),count:n.ai()})),byProcedure:n.YO(n.Ik({_id:n.Yj(),count:n.ai()}))})});let r=({courtCase:a,onSubmit:b,onCancel:c,loading:g=!1})=>{let h=!!a,{register:i,handleSubmit:j,formState:{errors:k},reset:n,setValue:q,watch:r}=(0,l.mN)({resolver:(0,m.u)(p),defaultValues:{trangThaiGiaiQuyet:"Chưa giải quyết"}});return(0,e.useEffect)(()=>{if(a){let b=a.ngayNhanVanThu?new Date(a.ngayNhanVanThu).toISOString().split("T")[0]:"",c=a.ngayThuLy?new Date(a.ngayThuLy).toISOString().split("T")[0]:"",d=a.ngayBanHanh?new Date(a.ngayBanHanh).toISOString().split("T")[0]:"";n({...a,ngayNhanVanThu:b,ngayThuLy:c,ngayBanHanh:d})}},[a,n]),(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsxs)("div",{className:"p-6 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:h?"Chỉnh sửa vụ việc":"Th\xeam vụ việc mới"}),(0,d.jsx)("button",{onClick:c,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("form",{onSubmit:j(a=>{try{b(a)}catch(a){console.error("Form submission error:",a),f.oR.error("C\xf3 lỗi xảy ra khi gửi form")}}),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",style:{color:"#111827"},children:"Th\xf4ng tin thụ l\xfd"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Số văn thư"}),(0,d.jsx)("input",{type:"text",...i("soVanThu"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập số văn thư"}),k.soVanThu&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.soVanThu.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Ng\xe0y nhận văn thư"}),(0,d.jsx)("input",{type:"date",...i("ngayNhanVanThu"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}}),k.ngayNhanVanThu&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.ngayNhanVanThu.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Loại \xe1n"}),(0,d.jsx)("input",{type:"text",...i("loaiAn"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập loại \xe1n (H\xecnh sự, D\xe2n sự, H\xe0nh ch\xednh, Kinh tế, Lao động, Kh\xe1c...)"}),k.loaiAn&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.loaiAn.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Số thụ l\xfd"}),(0,d.jsx)("input",{type:"text",...i("soThuLy"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập số thụ l\xfd"}),k.soThuLy&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.soThuLy.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Ng\xe0y thụ l\xfd"}),(0,d.jsx)("input",{type:"date",...i("ngayThuLy"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}}),k.ngayThuLy&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.ngayThuLy.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"TAND"}),(0,d.jsx)("input",{type:"text",...i("tand"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"T\xf2a \xe1n nh\xe2n d\xe2n"}),k.tand&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.tand.message})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",style:{color:"#111827"},children:"Th\xf4ng tin bản \xe1n/quyết định"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Số bản \xe1n/quyết định"}),(0,d.jsx)("input",{type:"text",...i("soBanAn"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập số bản \xe1n/quyết định"}),k.soBanAn&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.soBanAn.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Ng\xe0y ban h\xe0nh"}),(0,d.jsx)("input",{type:"date",...i("ngayBanHanh"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}}),k.ngayBanHanh&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.ngayBanHanh.message})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Bị c\xe1o/Nguy\xean đơn/Người khiếu kiện"}),(0,d.jsx)("textarea",{...i("biCaoNguoiKhieuKien"),rows:2,className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập th\xf4ng tin bị c\xe1o/nguy\xean đơn/người khiếu kiện"}),k.biCaoNguoiKhieuKien&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.biCaoNguoiKhieuKien.message})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Tội danh/Bị đơn/Người bị kiện"}),(0,d.jsx)("textarea",{...i("toiDanhNoiDung"),rows:3,className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập tội danh/bị đơn/người bị kiện"}),k.toiDanhNoiDung&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.toiDanhNoiDung.message})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Tội danh/Quan hệ ph\xe1p luật"}),(0,d.jsx)("textarea",{...i("quanHePhatLuat"),rows:2,className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập tội danh/quan hệ ph\xe1p luật"}),k.quanHePhatLuat&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.quanHePhatLuat.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"H\xecnh thức xử l\xfd"}),(0,d.jsx)("input",{type:"text",...i("hinhThucXuLy"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập h\xecnh thức xử l\xfd"}),k.hinhThucXuLy&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.hinhThucXuLy.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Thủ tục \xe1p dụng"}),(0,d.jsx)("input",{type:"text",...i("thuTucApDung"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập thủ tục \xe1p dụng (Sơ thẩm, Ph\xfac thẩm, Gi\xe1m đốc thẩm, T\xe1i thẩm...)"}),k.thuTucApDung&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.thuTucApDung.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Thẩm ph\xe1n phụ tr\xe1ch"}),(0,d.jsx)("input",{type:"text",...i("thamPhanPhuTrach"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập t\xean thẩm ph\xe1n phụ tr\xe1ch"}),k.thamPhanPhuTrach&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.thamPhanPhuTrach.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Trưởng/Ph\xf3 ph\xf2ng KTNV/Thẩm tra vi\xean"}),(0,d.jsx)("input",{type:"text",...i("truongPhoPhongKTNV"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập t\xean trưởng/ph\xf3 ph\xf2ng KTNV/thẩm tra vi\xean"}),k.truongPhoPhongKTNV&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.truongPhoPhongKTNV.message})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",style:{color:"#111827"},children:"Th\xf4ng tin bổ sung"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Trạng th\xe1i giải quyết"}),(0,d.jsx)("select",{...i("trangThaiGiaiQuyet"),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},children:o.options.map(a=>(0,d.jsx)("option",{value:a,children:a},a))}),k.trangThaiGiaiQuyet&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.trangThaiGiaiQuyet.message})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Ghi ch\xfa"}),(0,d.jsx)("textarea",{...i("ghiChu"),rows:3,className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập ghi ch\xfa (t\xf9y chọn)"}),k.ghiChu&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.ghiChu.message})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Ghi ch\xfa kết quả"}),(0,d.jsx)("textarea",{...i("ghiChuKetQua"),rows:3,className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},placeholder:"Nhập ghi ch\xfa kết quả (t\xf9y chọn)"}),k.ghiChuKetQua&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.ghiChuKetQua.message})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,d.jsx)("button",{type:"button",onClick:c,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},disabled:g,children:"Hủy"}),(0,d.jsx)("button",{type:"submit",className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#2563eb",color:"#ffffff"},disabled:g,children:g?"Đang xử l\xfd...":h?"Cập nhật":"Th\xeam mới"})]})]})]})})},s=({courtCase:a,onClose:b,onEdit:c,onDelete:e})=>{let{hasPermission:f}=(0,j.S)();return(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsxs)("div",{className:"p-6 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"text-3xl mr-3",children:(a=>{if(!a)return"\uD83D\uDCC4";let b=a.toLowerCase();return b.includes("h\xecnh sự")?"⚖️":b.includes("d\xe2n sự")?"\uD83C\uDFE0":b.includes("h\xe0nh ch\xednh")?"\uD83C\uDFDB️":b.includes("kinh tế")?"\uD83D\uDCBC":b.includes("lao động")?"\uD83D\uDC77":"\uD83D\uDCCB"})(a.loaiAn)}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:["Chi tiết vụ việc #",a.stt]}),(0,d.jsx)("p",{className:"text-gray-600",children:a.soThuLy})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDCCB Th\xf4ng tin thụ l\xfd"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"STT"}),(0,d.jsx)("p",{className:"text-lg font-semibold",style:{color:"#111827"},children:a.stt})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Loại \xe1n"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.loaiAn})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Số thụ l\xfd"}),(0,d.jsx)("p",{className:"text-lg font-mono",style:{color:"#111827"},children:a.soThuLy})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y thụ l\xfd"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,i.Yq)(a.ngayThuLy)})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"TAND"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.tand})]})]})]}),(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"⚖️ Th\xf4ng tin bản \xe1n/quyết định"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Số bản \xe1n/quyết định"}),(0,d.jsx)("p",{className:"text-lg font-mono",style:{color:"#111827"},children:a.soBanAn})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y ban h\xe0nh"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,i.Yq)(a.ngayBanHanh)})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Bị c\xe1o/Nguy\xean đơn/Người khiếu kiện"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.biCaoNguoiKhieuKien})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Tội danh/Bồi dưỡng/Nội dung khiếu kiện"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.toiDanhNoiDung})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Tội danh/Quan hệ ph\xe1p luật"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.quanHePhatLuat})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"H\xecnh thức xử l\xfd"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.hinhThucXuLy})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Thủ tục \xe1p dụng"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.thuTucApDung})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Thẩm ph\xe1n phụ tr\xe1ch"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.thamPhanPhuTrach})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Trưởng/Ph\xf3 ph\xf2ng KTNV/Thẩm tra vi\xean"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.truongPhoPhongKTNV})]})]})]}),(0,d.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDCDD Th\xf4ng tin bổ sung"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Trạng th\xe1i giải quyết"}),(0,d.jsx)("span",{className:`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${(a=>{switch(a){case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800";case"Chưa giải quyết":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.trangThaiGiaiQuyet)}`,children:a.trangThaiGiaiQuyet})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y cập nhật cuối"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,i.Yq)(a.updatedAt)})]}),a.ghiChu&&(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ghi ch\xfa"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.ghiChu})]}),a.ghiChuKetQua&&(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ghi ch\xfa kết quả"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.ghiChuKetQua})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDC64 Th\xf4ng tin hệ thống"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người tạo"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.createdBy?.username||"N/A"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y tạo"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,i.Yq)(a.createdAt)})]}),a.updatedBy&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người cập nhật cuối"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.updatedBy.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y cập nhật cuối"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,i.Yq)(a.updatedAt)})]})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:[(0,d.jsx)("button",{onClick:b,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"}),f("court_case_edit")&&(0,d.jsx)("button",{onClick:()=>{b(),c(a)},className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#059669",color:"#ffffff"},children:"Chỉnh sửa"}),f("court_case_delete")&&(0,d.jsx)("button",{onClick:()=>{confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?")&&(e(a._id),b())},className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"X\xf3a"})]})]})})};var t=c(81391),u=c(24224),v=c(4780);let w=(0,u.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),x=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},g)=>{let h=e?t.DX:"button";return(0,d.jsx)(h,{className:(0,v.cn)(w({variant:b,size:c,className:a})),ref:g,...f})});x.displayName="Button";var y=c(28749),z=c(71170),A=c(62688);let B=(0,A.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),C=(0,A.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),D=(0,A.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),E=(0,A.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),F=(0,A.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),G=(0,A.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),H=({preview:a,onConfirmImport:b,onCancel:c,isImporting:f})=>{let[g,h]=(0,e.useState)(!1),[i,j]=(0,e.useState)("all"),k="all"===i?a.data:a.data.filter(a=>a.status===i);return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Xem trước dữ liệu import"}),(0,d.jsxs)(x,{variant:"outline",onClick:c,className:"text-gray-600 hover:text-gray-800",children:[(0,d.jsx)(E,{className:"h-4 w-4 mr-2"}),"Đ\xf3ng"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)(y.Zp,{children:[(0,d.jsx)(y.aR,{className:"pb-2",children:(0,d.jsx)(y.ZB,{className:"text-sm font-medium text-gray-600",children:"Tổng số d\xf2ng"})}),(0,d.jsx)(y.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.summary.totalRows})})]}),(0,d.jsxs)(y.Zp,{children:[(0,d.jsx)(y.aR,{className:"pb-2",children:(0,d.jsx)(y.ZB,{className:"text-sm font-medium text-gray-600",children:"Hợp lệ"})}),(0,d.jsx)(y.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.summary.validRows})})]}),(0,d.jsxs)(y.Zp,{children:[(0,d.jsx)(y.aR,{className:"pb-2",children:(0,d.jsx)(y.ZB,{className:"text-sm font-medium text-gray-600",children:"Cảnh b\xe1o"})}),(0,d.jsx)(y.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:a.summary.warningRows})})]}),(0,d.jsxs)(y.Zp,{children:[(0,d.jsx)(y.aR,{className:"pb-2",children:(0,d.jsx)(y.ZB,{className:"text-sm font-medium text-gray-600",children:"Lỗi"})}),(0,d.jsx)(y.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.summary.errorRows})})]})]}),a.warnings.length>0&&(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(C,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-yellow-800 mb-2",children:"Cảnh b\xe1o:"}),(0,d.jsx)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:a.warnings.map((a,b)=>(0,d.jsx)("li",{className:"text-sm",children:a},b))})]})]})}),a.errors.length>0&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(D,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-red-800 mb-2",children:"Lỗi:"}),(0,d.jsx)("ul",{className:"list-disc list-inside space-y-1 text-red-700",children:a.errors.map((a,b)=>(0,d.jsx)("li",{className:"text-sm",children:a},b))})]})]})}),(0,d.jsx)(y.Zp,{children:(0,d.jsx)(y.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{children:a.summary.canImport?(0,d.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,d.jsx)(B,{className:"h-5 w-5 mr-2"}),(0,d.jsx)("span",{className:"font-medium",children:"C\xf3 thể import được"})]}):(0,d.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,d.jsx)(D,{className:"h-5 w-5 mr-2"}),(0,d.jsx)("span",{className:"font-medium",children:"Kh\xf4ng thể import do c\xf3 lỗi"})]})}),(0,d.jsxs)("div",{className:"space-x-2",children:[(0,d.jsxs)(x,{variant:"outline",onClick:()=>h(!g),children:[(0,d.jsx)(F,{className:"h-4 w-4 mr-2"}),g?"Ẩn chi tiết":"Xem chi tiết"]}),(0,d.jsxs)(x,{onClick:b,disabled:!a.summary.canImport||f,className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(G,{className:"h-4 w-4 mr-2"}),f?"Đang import...":"X\xe1c nhận import"]})]})]})})}),g&&(0,d.jsxs)(y.Zp,{children:[(0,d.jsx)(y.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(y.ZB,{children:"Chi tiết dữ liệu"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(x,{variant:"all"===i?"default":"outline",size:"sm",onClick:()=>j("all"),children:["Tất cả (",a.summary.totalRows,")"]}),(0,d.jsxs)(x,{variant:"valid"===i?"default":"outline",size:"sm",onClick:()=>j("valid"),children:["Hợp lệ (",a.summary.validRows,")"]}),(0,d.jsxs)(x,{variant:"warning"===i?"default":"outline",size:"sm",onClick:()=>j("warning"),children:["Cảnh b\xe1o (",a.summary.warningRows,")"]}),(0,d.jsxs)(x,{variant:"error"===i?"default":"outline",size:"sm",onClick:()=>j("error"),children:["Lỗi (",a.summary.errorRows,")"]})]})]})}),(0,d.jsx)(y.Wu,{children:(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"bg-gray-50",children:[(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"D\xf2ng"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Trạng th\xe1i"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Số thụ l\xfd"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Loại \xe1n"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Ng\xe0y thụ l\xfd"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Bị c\xe1o/NĐ/NKK"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Lỗi/Cảnh b\xe1o"})]})}),(0,d.jsx)("tbody",{children:k.map((a,b)=>(0,d.jsxs)("tr",{className:`
                      ${"error"===a.status?"bg-red-50":"warning"===a.status?"bg-yellow-50":"bg-green-50"}
                    `,children:[(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.rowNumber}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(a=>{switch(a){case"valid":return(0,d.jsx)(B,{className:"h-4 w-4 text-green-500"});case"warning":return(0,d.jsx)(C,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,d.jsx)(D,{className:"h-4 w-4 text-red-500"});default:return null}})(a.status),(0,d.jsx)("span",{className:"ml-2",children:(a=>{switch(a){case"valid":return(0,d.jsx)(z.Ex,{variant:"success",children:"Hợp lệ"});case"warning":return(0,d.jsx)(z.Ex,{variant:"warning",children:"Cảnh b\xe1o"});case"error":return(0,d.jsx)(z.Ex,{variant:"danger",children:"Lỗi"});default:return null}})(a.status)})]})}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.soThuLy}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.loaiAn}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.ngayThuLyDisplay||""}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2 max-w-xs truncate",children:a.biCaoNguoiKhieuKien}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.validationErrors.length>0&&(0,d.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:a.validationErrors.map((a,b)=>(0,d.jsxs)("li",{children:["• ",a]},b))})})]},b))})]})})})]})]})},I=({onImportComplete:a,onClose:b})=>{let[c,g]=(0,e.useState)(null),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)(null),q=(0,e.useRef)(null),r=async()=>{if(!c)return void f.oR.error("Vui l\xf2ng chọn file Excel");try{l(!0);let a=await h.previewImport(c);a.payload.success?n(a.payload.preview):f.oR.error("Kh\xf4ng thể xem trước file")}catch(a){console.error("Error previewing:",a),f.oR.error("C\xf3 lỗi xảy ra khi xem trước file")}finally{l(!1)}},s=async()=>{if(!c)return void f.oR.error("Vui l\xf2ng chọn file Excel");try{j(!0);let b=await h.importCourtCases(c);b.payload.success?(p(b.payload.results),n(null),f.oR.success(`Import th\xe0nh c\xf4ng! ${b.payload.results.success}/${b.payload.results.total} vụ việc`),a()):f.oR.error("Import thất bại")}catch(a){console.error("Error importing:",a),f.oR.error("C\xf3 lỗi xảy ra khi import file")}finally{j(!1)}},t=async()=>{try{await h.downloadTemplate(),f.oR.success("Đ\xe3 tải xuống file mẫu")}catch(a){console.error("Error downloading template:",a),f.oR.error("Kh\xf4ng thể tải xuống file mẫu")}};return(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsx)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:m?(0,d.jsx)(H,{preview:m,onConfirmImport:s,onCancel:()=>n(null),isImporting:i}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCE5 Import vụ việc từ Excel"}),(0,d.jsx)("button",{onClick:b,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Hướng dẫn:"}),(0,d.jsxs)("ul",{className:"text-sm space-y-1",style:{color:"#111827"},children:[(0,d.jsx)("li",{children:"• File Excel phải c\xf3 định dạng .xlsx hoặc .xls"}),(0,d.jsx)("li",{children:"• D\xf2ng đầu ti\xean l\xe0 ti\xeau đề cột (sẽ bị bỏ qua)"}),(0,d.jsx)("li",{children:"• STT c\xf3 thể để trống (hệ thống tự tạo)"}),(0,d.jsx)("li",{children:"• Ng\xe0y th\xe1ng theo định dạng YYYY-MM-DD hoặc DD/MM/YYYY"}),(0,d.jsx)("li",{children:"• C\xe1c trường kh\xe1c c\xf3 thể để trống"}),(0,d.jsxs)("li",{children:["• ",(0,d.jsx)("strong",{children:"Khuyến kh\xedch xem trước dữ liệu trước khi import"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center p-4 border border-gray-200 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCC4 File mẫu"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tham khảo định dạng"})]}),(0,d.jsx)("button",{onClick:t,className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Tải file mẫu"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Chọn file Excel"}),(0,d.jsx)("input",{ref:q,type:"file",accept:".xlsx,.xls",onChange:a=>{let b=a.target.files?.[0];if(b){if(!b.name.endsWith(".xlsx")&&!b.name.endsWith(".xls"))return void f.oR.error("Vui l\xf2ng chọn file Excel (.xlsx hoặc .xls)");g(b),p(null),n(null)}},className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),c&&(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("p",{className:"text-sm",style:{color:"#111827"},children:[(0,d.jsx)("strong",{children:"File đ\xe3 chọn:"})," ",c.name]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["K\xedch thước: ",(c.size/1024).toFixed(2)," KB"]})]})]}),o&&(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCCA Kết quả import:"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:o.total}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng số d\xf2ng"})]}),(0,d.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.success}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Th\xe0nh c\xf4ng"})]}),(0,d.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:o.duplicates}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tr\xf9ng lặp"})]}),(0,d.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:o.errors.length}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Lỗi"})]})]}),o.errors.length>0&&(0,d.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,d.jsx)("h5",{className:"font-medium text-red-800 mb-2",children:"❌ Lỗi chi tiết:"}),(0,d.jsx)("div",{className:"max-h-32 overflow-y-auto",children:o.errors.map((a,b)=>(0,d.jsxs)("p",{className:"text-sm text-red-700",children:["• ",a]},b))})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,d.jsx)("button",{onClick:b,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},disabled:i||k,children:"Đ\xf3ng"}),(0,d.jsx)("button",{onClick:r,disabled:!c||i||k,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#059669",color:"#ffffff"},children:k?"Đang xem trước...":"\uD83D\uDC41️ Xem trước"}),(0,d.jsx)("button",{onClick:s,disabled:!c||i||k,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:i?"Đang import...":"\uD83D\uDCE5 Import trực tiếp"})]})]})]})})})},J=({onClose:a})=>{let[b,c]=(0,e.useState)(null),[g,i]=(0,e.useState)(!1),[j,k]=(0,e.useState)({fromDate:"",toDate:"",groupBy:"month"}),l=async()=>{try{i(!0);let a=await h.getDetailedStats(j);a.payload.success?c(a.payload.stats):f.oR.error("Kh\xf4ng thể tải thống k\xea chi tiết")}catch(a){console.error("Error fetching detailed stats:",a),f.oR.error("C\xf3 lỗi xảy ra khi tải thống k\xea")}finally{i(!1)}};(0,e.useEffect)(()=>{l()},[j]);let m=(a,b)=>{k(c=>({...c,[a]:b}))},n=async()=>{try{f.oR.info("Đang xuất b\xe1o c\xe1o chi tiết...");let a=await h.exportCourtCases(j),b=new Blob([a.payload],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),c=window.URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`bao-cao-chi-tiet-vu-viec-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(c),f.oR.success("Xuất b\xe1o c\xe1o th\xe0nh c\xf4ng")}catch(a){console.error("Error exporting detailed report:",a),f.oR.error("C\xf3 lỗi xảy ra khi xuất b\xe1o c\xe1o")}};return(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsxs)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCCA Thống k\xea chi tiết vụ việc"}),(0,d.jsx)("button",{onClick:a,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDD0D Bộ lọc"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Từ ng\xe0y"}),(0,d.jsx)("input",{type:"date",value:j.fromDate,onChange:a=>m("fromDate",a.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Đến ng\xe0y"}),(0,d.jsx)("input",{type:"date",value:j.toDate,onChange:a=>m("toDate",a.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Nh\xf3m theo"}),(0,d.jsxs)("select",{value:j.groupBy,onChange:a=>m("groupBy",a.target.value),className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"},children:[(0,d.jsx)("option",{value:"day",children:"Ng\xe0y"}),(0,d.jsx)("option",{value:"week",children:"Tuần"}),(0,d.jsx)("option",{value:"month",children:"Th\xe1ng"}),(0,d.jsx)("option",{value:"quarter",children:"Qu\xfd"}),(0,d.jsx)("option",{value:"year",children:"Năm"})]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsx)("button",{onClick:n,className:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"\uD83D\uDCCA Xuất b\xe1o c\xe1o"})})]})]}),g?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-600",children:"Đang tải thống k\xea..."})]}):b?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Tổng quan"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Tổng số vụ việc:"}),(0,d.jsx)("span",{className:"ml-2 font-bold text-2xl text-blue-600",children:b.summary.total})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Thời gian:"}),(0,d.jsx)("span",{className:"ml-2 font-medium",children:b.summary.period})]})]})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDCC8 Ph\xe2n bố theo trạng th\xe1i"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:b.byStatus.map(a=>(0,d.jsxs)("div",{className:`p-4 rounded-lg ${(a=>{switch(a){case"Đ\xe3 giải quyết":return"text-green-600 bg-green-100";case"Đang giải quyết":return"text-yellow-600 bg-yellow-100";case"Chưa giải quyết":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(a._id)}`,children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:a.count}),(0,d.jsx)("div",{className:"text-sm",children:a._id||"Kh\xf4ng x\xe1c định"})]},a._id))})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"⚖️ Ph\xe2n bố theo loại \xe1n"}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:b.byType.map(a=>(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-xl font-bold text-gray-700",children:a.count}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a._id||"Kh\xe1c"})]},a._id))})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDD28 Ph\xe2n bố theo h\xecnh thức xử l\xfd"}),(0,d.jsx)("div",{className:"space-y-2",children:b.byProcessingMethod.slice(0,8).map(a=>(0,d.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,d.jsx)("span",{className:"text-sm",children:a._id||"Kh\xf4ng x\xe1c định"}),(0,d.jsx)("span",{className:"font-bold text-blue-600",children:a.count})]},a._id))})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDC68‍⚖️ Ph\xe2n bố theo Thẩm ph\xe1n"}),(0,d.jsx)("div",{className:"space-y-2",children:b.topJudges.slice(0,10).map((a,b)=>(0,d.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"w-6 h-6 bg-blue-600 text-white rounded-full text-xs flex items-center justify-center mr-3",children:b+1}),(0,d.jsx)("span",{className:"text-sm",children:a._id})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"font-bold text-blue-600",children:[a.count," vụ"]}),(0,d.jsxs)("div",{className:"text-xs text-green-600",children:[a.resolved," đ\xe3 giải quyết"]})]})]},a._id))})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83C\uDFDB️ Ph\xe2n bố theo t\xf2a \xe1n"}),(0,d.jsx)("div",{className:"space-y-2",children:b.byCourt.slice(0,10).map(a=>(0,d.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,d.jsx)("span",{className:"text-sm",children:a._id||"Kh\xf4ng x\xe1c định"}),(0,d.jsx)("span",{className:"font-bold text-purple-600",children:a.count})]},a._id))})]}),(0,d.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",style:{color:"#111827"},children:"\uD83D\uDCC8 Xu hướng theo thời gian"}),(0,d.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:b.trends.map(a=>(0,d.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:a._id}),(0,d.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,d.jsxs)("span",{className:"text-blue-600",children:["Tổng: ",a.count]}),(0,d.jsxs)("span",{className:"text-green-600",children:["Đ\xe3 giải quyết: ",a.resolved]}),(0,d.jsxs)("span",{className:"text-yellow-600",children:["Đang giải quyết: ",a.inProgress]}),(0,d.jsxs)("span",{className:"text-red-600",children:["Chưa giải quyết: ",a.pending]})]})]},a._id))})]})]}):(0,d.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Kh\xf4ng c\xf3 dữ liệu thống k\xea"}),(0,d.jsx)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:(0,d.jsx)("button",{onClick:a,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"})})]})})},K=({searchParams:a,onSearch:b,onReset:c})=>{let[f,g]=(0,e.useState)(!1),h=Object.values({search:a.search,loaiAn:a.loaiAn,trangThaiGiaiQuyet:a.trangThaiGiaiQuyet,thuTucApDung:a.thuTucApDung,fromDate:a.fromDate,toDate:a.toDate}).filter(a=>a&&""!==a).length;return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>{g(!f)},children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"text-xl",children:"\uD83D\uDD0D"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Bộ lọc v\xe0 t\xecm kiếm"}),h>0&&(0,d.jsxs)("p",{className:"text-sm text-blue-600",children:[h," bộ lọc đang được \xe1p dụng"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[!f&&h>0&&(0,d.jsx)("button",{onClick:a=>{a.stopPropagation(),c()},className:"px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors",children:"X\xf3a tất cả"}),(0,d.jsx)("div",{className:`transform transition-transform duration-200 ${f?"rotate-180":""}`,children:(0,d.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,d.jsx)("div",{className:`transition-all duration-300 ease-in-out ${f?"max-h-96 opacity-100":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,d.jsx)("div",{className:"px-4 pb-4 border-t border-gray-100",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xecm kiếm"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",value:a.search||"",onChange:a=>b({search:a.target.value}),placeholder:"Số thụ l\xfd, bản \xe1n, t\xean...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDD0D"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Loại \xe1n"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",value:a.loaiAn||"",onChange:a=>b({loaiAn:a.target.value}),placeholder:"Nhập loại \xe1n để t\xecm kiếm...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"⚖️"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trạng th\xe1i"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("select",{value:a.trangThaiGiaiQuyet||"",onChange:a=>b({trangThaiGiaiQuyet:a.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white",children:[(0,d.jsx)("option",{value:"",children:"Tất cả"}),o.options.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCCA"}),(0,d.jsx)("div",{className:"absolute right-2 top-2.5 text-gray-400 pointer-events-none",children:(0,d.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Thủ tục \xe1p dụng"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",value:a.thuTucApDung||"",onChange:a=>b({thuTucApDung:a.target.value}),placeholder:"Nhập thủ tục \xe1p dụng để t\xecm kiếm...",className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCDD"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Từ ng\xe0y"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"date",value:a.fromDate||"",onChange:a=>b({fromDate:a.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCC5"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Đến ng\xe0y"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"date",value:a.toDate||"",onChange:a=>b({toDate:a.target.value}),className:"w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("div",{className:"absolute left-2 top-2.5 text-gray-400",children:"\uD83D\uDCC5"})]})]}),(0,d.jsx)("div",{className:"flex items-end gap-2",children:(0,d.jsx)("button",{onClick:c,className:"flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors",children:"\uD83D\uDDD1️ X\xf3a bộ lọc"})})]})})})]})};var L=c(98462);let M=()=>{let{hasPermission:a}=(0,j.S)(),[b,c]=(0,e.useState)([]),[g,i]=(0,e.useState)(!1),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)(!1),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)(null),[z,A]=(0,e.useState)({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"}),[B,C]=(0,e.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:20,hasNextPage:!1,hasPrevPage:!1}),D=async()=>{try{i(!0);let a=await h.getCourtCases(z);a.payload.success?(c(a.payload.cases),C(a.payload.pagination)):f.oR.error("Kh\xf4ng thể tải danh s\xe1ch vụ việc")}catch(a){console.error("Error fetching court cases:",a),f.oR.error("C\xf3 lỗi xảy ra khi tải danh s\xe1ch vụ việc")}finally{i(!1)}},E=async()=>{if(a("court_case_stats_view"))try{let a=await h.getCourtCaseStats();a.payload.success&&y(a.payload.stats)}catch(a){console.error("Error fetching stats:",a)}};(0,e.useEffect)(()=>{D(),E()},[z]);let F=a=>{A(b=>({...b,page:a}))},G=async a=>{try{i(!0),(await h.createCourtCase(a)).payload.success?(f.oR.success("Th\xeam vụ việc th\xe0nh c\xf4ng"),q(!1),D(),E()):f.oR.error("Kh\xf4ng thể th\xeam vụ việc")}catch(a){console.error("Error creating court case:",a),f.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi th\xeam vụ việc")}finally{i(!1)}},H=async a=>{if(n)try{i(!0),(await h.updateCourtCase(n._id,a)).payload.success?(f.oR.success("Cập nhật vụ việc th\xe0nh c\xf4ng"),o(null),D(),E()):f.oR.error("Kh\xf4ng thể cập nhật vụ việc")}catch(a){console.error("Error updating court case:",a),f.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi cập nhật vụ việc")}finally{i(!1)}},M=async a=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?"))try{(await h.deleteCourtCase(a)).payload.success?(f.oR.success("X\xf3a vụ việc th\xe0nh c\xf4ng"),D(),E()):f.oR.error("Kh\xf4ng thể x\xf3a vụ việc")}catch(a){console.error("Error deleting court case:",a),f.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi x\xf3a vụ việc")}},N=async(a,b)=>{if("delete"===b)try{await h.bulkDeleteCourtCases(a),f.oR.success(`Đ\xe3 x\xf3a ${a.length} vụ việc`),D(),E()}catch(a){console.error("Error bulk deleting:",a),f.oR.error("C\xf3 lỗi xảy ra khi x\xf3a h\xe0ng loạt")}},O=async()=>{try{f.oR.info("Đang xuất file Excel...");let a=await h.exportCourtCases(z),b=new Blob([a.payload],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),c=window.URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`danh-sach-vu-viec-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(c),f.oR.success("Xuất file Excel th\xe0nh c\xf4ng")}catch(a){console.error("Error exporting Excel:",a),f.oR.error("C\xf3 lỗi xảy ra khi xuất file Excel")}};return(0,d.jsx)(L.default,{requiredPermissions:["court_case_view"],children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Quản l\xfd vụ việc t\xf2a \xe1n"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Quản l\xfd danh s\xe1ch thụ l\xfd v\xe0 giải quyết vụ việc đề nghị gi\xe1m đốc thẩm, t\xe1i thẩm"})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[a("court_case_stats_view")&&(0,d.jsx)("button",{onClick:()=>w(!0),className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"\uD83D\uDCCA Thống k\xea chi tiết"}),a("court_case_import")&&(0,d.jsx)("button",{onClick:()=>u(!0),className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500",children:"\uD83D\uDCE5 Import Excel"}),a("court_case_export")&&(0,d.jsx)("button",{onClick:O,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500",children:"\uD83D\uDCCA Xuất Excel"}),a("court_case_create")&&(0,d.jsx)("button",{onClick:()=>q(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"➕ Th\xeam vụ việc mới"})]})]}),a("court_case_stats_view")&&x&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-3xl mr-4",children:"\uD83D\uDCCA"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tổng số vụ việc"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:x.total})]})]})}),x.byStatus.map(a=>(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-3xl mr-4",children:{"Chưa giải quyết":"\uD83D\uDD34","Đang giải quyết":"\uD83D\uDFE1","Đ\xe3 giải quyết":"\uD83D\uDFE2"}[a._id]||"\uD83D\uDCCB"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:a._id}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.count})]})]})},a._id))]}),(0,d.jsx)(K,{searchParams:z,onSearch:a=>{A(b=>({...b,...a,page:1}))},onReset:()=>{A({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"})}}),(0,d.jsx)(k,{cases:b,onCaseSelect:m,onCaseEdit:o,onCaseDelete:M,onBulkAction:N,onSort:(a,b)=>{A(c=>({...c,sortBy:a,sortOrder:b,page:1}))},currentSort:{sortBy:z.sortBy,sortOrder:z.sortOrder},loading:g}),B.totalPages>1&&(0,d.jsx)("div",{className:"bg-white px-6 py-3 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-700",children:["Hiển thị ",(B.currentPage-1)*B.itemsPerPage+1," đến"," ",Math.min(B.currentPage*B.itemsPerPage,B.totalItems)," trong tổng số"," ",B.totalItems," vụ việc"]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>F(B.currentPage-1),disabled:!B.hasPrevPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Trước"}),(0,d.jsxs)("span",{className:"px-3 py-2 text-sm text-gray-700",children:["Trang ",B.currentPage," / ",B.totalPages]}),(0,d.jsx)("button",{onClick:()=>F(B.currentPage+1),disabled:!B.hasNextPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Sau"})]})]})}),p&&(0,d.jsx)(r,{onSubmit:G,onCancel:()=>q(!1),loading:g}),n&&(0,d.jsx)(r,{courtCase:n,onSubmit:H,onCancel:()=>o(null),loading:g}),l&&(0,d.jsx)(s,{courtCase:l,onClose:()=>m(null),onEdit:o,onDelete:M}),t&&(0,d.jsx)(I,{onImportComplete:()=>{D(),E()},onClose:()=>u(!1)}),v&&(0,d.jsx)(J,{onClose:()=>w(!1)})]})})}},28749:(a,b,c)=>{"use strict";c.d(b,{CN:()=>i,Lz:()=>j,Wu:()=>h,ZB:()=>g,Zp:()=>e,aR:()=>f});var d=c(60687);c(43210);let e=({children:a,className:b="",padding:c="md",shadow:e="sm",hover:f=!1,onClick:g})=>(0,d.jsx)("div",{className:`
        bg-white rounded-xl border border-gray-100
        ${{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[c]}
        ${{none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[e]}
        ${f?"hover:shadow-lg transition-shadow duration-200":""}
        ${b}
      `,onClick:g,children:a}),f=({children:a,className:b=""})=>(0,d.jsx)("div",{className:`border-b border-gray-100 pb-4 mb-6 ${b}`,children:a}),g=({children:a,className:b="",size:c="md"})=>(0,d.jsx)("h2",{className:`font-bold text-gray-900 ${{sm:"text-lg",md:"text-xl",lg:"text-2xl"}[c]} ${b}`,children:a}),h=({children:a,className:b=""})=>(0,d.jsx)("div",{className:b,children:a}),i=({title:a,value:b,icon:c,trend:f,color:g="blue",onClick:h,clickable:i=!1})=>{let j=(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:a}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof b?b.toLocaleString():b}),f&&(0,d.jsxs)("p",{className:`text-sm mt-2 flex items-center ${f.isPositive?"text-green-600":"text-red-600"}`,children:[(0,d.jsx)("span",{className:"mr-1",children:f.isPositive?"↗":"↘"}),f.value]})]}),c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[g]}`,children:(0,d.jsx)("div",{className:"text-white",children:c})})]});return i&&h?(0,d.jsx)(e,{hover:!0,className:`relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ${i?"hover:shadow-lg":""}`,onClick:h,children:j}):(0,d.jsx)(e,{hover:!0,className:"relative overflow-hidden",children:j})},j=({title:a,description:b,icon:c,onClick:f,href:g,color:h="blue"})=>{let i=({children:a})=>(0,d.jsx)(e,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:a}),j=(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[h]} flex-shrink-0`,children:(0,d.jsx)("div",{className:"text-white",children:c})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:a}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b})]})]});return g?(0,d.jsx)("a",{href:g,children:(0,d.jsx)(i,{children:j})}):(0,d.jsx)("div",{onClick:f,children:(0,d.jsx)(i,{children:j})})}},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},36984:(a,b,c)=>{Promise.resolve().then(c.bind(c,28690))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66800:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>e,ZV:()=>f,z3:()=>d});let d=(a,b=2)=>{if(0===a)return"0 Bytes";let c=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,c)).toFixed(b<0?0:b))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][c]},e=a=>{if(!a)return"";let b=new Date(a);return isNaN(b.getTime())?"":b.toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit"})},f=a=>a.toLocaleString()},71170:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(60687);c(43210);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}}[a];return(0,d.jsx)(e,{variant:c.variant,className:b,children:c.label})}},79428:a=>{"use strict";a.exports=require("buffer")},80950:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\app\\\\(private)\\\\dashboard\\\\court-cases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93433:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["court-cases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,80950)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/court-cases/page",pathname:"/dashboard/court-cases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/court-cases/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isLoading:l}=(0,e.S)(),m=(0,f.useRouter)();if(l)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,2415,3581,1178],()=>b(b.s=93433));module.exports=c})();