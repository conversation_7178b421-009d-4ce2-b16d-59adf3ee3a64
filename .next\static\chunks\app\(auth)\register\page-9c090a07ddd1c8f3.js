(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{1957:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(95155),a=r(63560),o=r(62177),n=r(12115),i=r(75937),l=r(62523),c=r(42350),u=r(71592),d=r(35695),m=r(23348),h=r(20174),p=r(38543),g=r(72509),f=r(26070);let y=()=>{let[e,t]=(0,n.useState)(!1),{setUser:r}=(0,m.U)(),[y,x]=(0,n.useState)(null),[A,v]=(0,n.useState)(!1),j=(0,d.useRouter)(),[w,S]=(0,n.useState)(null);(0,n.useEffect)(()=>{v("localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname)},[]);let b=(0,o.mN)({resolver:(0,a.u)(c.aU),defaultValues:{email:"",username:"",password:"",confirmPassword:""}});async function N(r){if(!A&&!y)return void p.oR.error("Vui l\xf2ng x\xe1c nhận reCAPTCHA!");if(e)return;t(!0);let s=await (0,f.I)();try{await u.A.register({...r,deviceId:s})&&(p.oR.success("Đăng k\xfd th\xe0nh c\xf4ng."),x(null),j.push("/login"),j.refresh())}catch(e){p.oR.error(e.payload.message),S(e.payload.message)}finally{t(!1)}}return(0,s.jsx)(i.lV,{...b,children:(0,s.jsxs)("form",{onSubmit:b.handleSubmit(N),className:"max-w-[600px] flex-shrink-0 w-full",noValidate:!0,children:[(0,s.jsx)(i.zB,{control:b.control,name:"username",render:e=>{let{field:t}=e;return(0,s.jsxs)(i.eI,{children:[(0,s.jsx)(i.MJ,{children:(0,s.jsx)(l.p,{placeholder:"username",...t})}),(0,s.jsx)(i.C5,{})]})}}),(0,s.jsx)(i.zB,{control:b.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(i.eI,{children:[(0,s.jsx)(i.MJ,{children:(0,s.jsx)(l.p,{placeholder:"email",type:"email",...t})}),(0,s.jsx)(i.C5,{})]})}}),(0,s.jsx)(i.zB,{control:b.control,name:"password",render:e=>{let{field:t}=e;return(0,s.jsxs)(i.eI,{children:[(0,s.jsx)(i.MJ,{children:(0,s.jsx)(l.p,{placeholder:"Mật khẩu",type:"password",...t})}),(0,s.jsx)(i.C5,{})]})}}),(0,s.jsx)(i.zB,{control:b.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,s.jsxs)(i.eI,{children:[(0,s.jsx)(i.MJ,{children:(0,s.jsx)(l.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...t})}),(0,s.jsx)(i.C5,{})]})}}),(0,s.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:w}),!A&&(0,s.jsx)(g.A,{sitekey:"6Lfqe6crAAAAAAlTAoCZ8bj3qXRlTt9l4qVSl8FG",onChange:e=>x(e)}),(0,s.jsxs)("button",{disabled:!!e,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:[e?(0,s.jsx)(h.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})})};var x=r(6874),A=r.n(x),v=r(99843);let j=()=>{let e=(0,d.useRouter)(),{setting:t}=(0,v.i)();return((0,n.useEffect)(()=>{t&&!1===t.openReg&&e.push("/login")},[t,e]),t)?(0,s.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,s.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,s.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,s.jsx)("h1",{className:"text-2xl text-center mb-4",children:"Đăng K\xfd"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(y,{})}),(0,s.jsx)("div",{className:"mt-6 relative text-center",children:(0,s.jsxs)("span",{children:["Bạn đ\xe3 c\xf3 t\xe0i khoản?",(0,s.jsx)(A(),{href:"/login",className:"text-blue-500 ml-1",children:"Đăng nhập tại đ\xe2y"})]})})]})})}):(0,s.jsx)("div",{className:"bg-gray-100 py-16 flex items-center justify-center",children:(0,s.jsx)("span",{className:"loading loading-dots loading-xl"})})}},14019:(e,t,r)=>{Promise.resolve().then(r.bind(r,1957))},23348:(e,t,r)=>{"use strict";r.d(t,{U:()=>n,default:()=>i});var s=r(95155),a=r(12115);let o=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),n=()=>(0,a.useContext)(o),i=e=>{let{children:t}=e,[r,n]=(0,a.useState)(()=>null),[i,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{n(e),localStorage.setItem("user",JSON.stringify(e))},[n]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");n(e?JSON.parse(e):null),l(!1)},[n]),(0,s.jsx)(o.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},26070:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var s=r(90524);let a=async()=>{let e=await s.Ay.load();return(await e.get()).visitorId}},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var s=r(84559),a=r(59434),o=r(35695);class n extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends n{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?s.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,m=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),h=await fetch(m,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),p=null,g=h.headers.get("content-type");if(g&&g.includes("application/json"))try{p=await h.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await h.text();let f={status:h.status,payload:p};if(!h.ok)if(404===h.status||403===h.status)throw new i(f);else if(401===h.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,o.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new n(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},42350:(e,t,r)=>{"use strict";r.d(t,{Ap:()=>i,ZZ:()=>l,aU:()=>a,ab:()=>n,iV:()=>o});var s=r(61612);let a=s.Ay.object({username:s.Ay.string().trim().min(2).max(256),email:s.Ay.string().email(),password:s.Ay.string().min(6).max(100),confirmPassword:s.Ay.string().min(6).max(100)}).strict().superRefine((e,t)=>{let{confirmPassword:r,password:s}=e;r!==s&&t.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})});s.Ay.object({token:s.Ay.string(),user:s.Ay.object({_id:s.Ay.number(),username:s.Ay.string(),email:s.Ay.string(),rule:s.Ay.string()}),message:s.Ay.string()});let o=s.Ay.object({email:s.Ay.string().email(),password:s.Ay.string().min(6).max(100),deviceId:s.Ay.string()}).strict(),n=s.Ay.object({email:s.Ay.string().email()}).strict(),i=s.Ay.object({email:s.Ay.string().email(),code:s.Ay.string(),password:s.Ay.string().min(6).max(100)}).strict();s.Ay.object({}).strict();let l=s.Ay.object({code:s.Ay.string().min(6),userId:s.Ay.string(),deviceId:s.Ay.string()}).strict();s.Ay.object({userId:s.Ay.string()}).strict()},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>n,cn:()=>o}),r(27937);var s=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}r(58801);let n=e=>e.startsWith("/")?e.slice(1):e},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155),a=r(59434),o=r(99310),n=r(59698),i=r(12115);let l=i.forwardRef((e,t)=>{let{className:r,type:l,...c}=e,[u,d]=(0,i.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"relative w-full",children:[(0,s.jsx)("input",{type:"password"===l&&u?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,a.cn)("input input-bordered w-full rounded-md",r),ref:t,...c}),"password"===l&&(u?(0,s.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>d(!u)}):(0,s.jsx)(n.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>d(!u)}))]})})});l.displayName="Input"},71592:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(27937);r(97967).config();let a={login:e=>s.Ay.post("/api/auth/login",e),register:e=>s.Ay.post("/api/auth/signup",e),forgot:e=>s.Ay.put("/api/auth/app-forgot-pass",e),changepass:e=>s.Ay.put("/api/auth/app-reset-pass",e),auth:e=>s.Ay.post("/api/auth",e,{baseUrl:""}),checkCode:e=>s.Ay.get("/api/auth/check-code/".concat(e)),VerifyAppCode:e=>s.Ay.post("/api/auth/verify-app-code",e),VerifyCode:e=>s.Ay.post("/api/auth/verify-code",e),logoutFromNextServerToServer:e=>s.Ay.post("/api/auth/blacklist-token/",e,{headers:{Authorization:"Bearer ".concat(e.sessionToken)}}),logoutFromNextClientToNextServer:(e,t)=>s.Ay.post("/api/auth/logout",{force:e},{baseUrl:"",signal:t}),slideSessionFromNextServerToServer:e=>s.Ay.post("/auth/slide-session",{},{headers:{Authorization:"Bearer ".concat(e)}}),slideSessionFromNextClientToNextServer:()=>s.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},72016:()=>{},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>d,MJ:()=>x,zB:()=>h,eI:()=>f,lR:()=>y,C5:()=>A});var s=r(95155),a=r(12115),o=r(54624),n=r(62177),i=r(59434),l=r(87073);let c=(0,r(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(l.b,{ref:t,className:(0,i.cn)(c(),r),...a})});u.displayName=l.b.displayName;let d=n.Op,m=a.createContext({}),h=e=>{let{...t}=e;return(0,s.jsx)(m.Provider,{value:{name:t.name},children:(0,s.jsx)(n.xI,{...t})})},p=()=>{let e=a.useContext(m),t=a.useContext(g),{getFieldState:r,formState:s}=(0,n.xW)(),o=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...o}},g=a.createContext({}),f=a.forwardRef((e,t)=>{let{className:r,...o}=e,n=a.useId();return(0,s.jsx)(g.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:t,className:(0,i.cn)("mb-4",r),...o})})});f.displayName="FormItem";let y=a.forwardRef((e,t)=>{let{className:r,...a}=e,{error:o,formItemId:n}=p();return(0,s.jsx)(u,{ref:t,className:(0,i.cn)(o&&"text-destructive",r),htmlFor:n,...a})});y.displayName="FormLabel";let x=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:n,formDescriptionId:i,formMessageId:l}=p();return(0,s.jsx)(o.DX,{ref:t,id:n,"aria-describedby":a?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!a,...r})});x.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:o}=p();return(0,s.jsx)("p",{ref:t,id:o,className:(0,i.cn)("text-[0.8rem] text-muted-foreground",r),...a})}).displayName="FormDescription";let A=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e,{error:n,formMessageId:l}=p(),c=n?String(null==n?void 0:n.message):a;return c?(0,s.jsx)("p",{ref:t,id:l,className:(0,i.cn)("text-[0.8rem] font-medium text-red-600",r),...o,children:c}):null});A.displayName="FormMessage"},83931:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(27937);let a={fetchSetting:e=>s.Ay.get("api/setting/admin",{headers:{Authorization:"Bearer ".concat(e)}}),commonFetchSetting:()=>s.Ay.get("api/setting/"),CraeteSetting:(e,t)=>s.Ay.put("api/setting/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditorSetting:(e,t)=>s.Ay.put("api/setting/editor",e,{headers:{Authorization:"Bearer ".concat(t)}}),CraeteMenu:(e,t)=>s.Ay.post("api/menu/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditMenu:(e,t)=>s.Ay.put("api/menu/edit",e,{headers:{Authorization:"Bearer ".concat(t)}}),GetMenu:e=>s.Ay.get("api/menu/".concat(e)),fetchMenus:(e,t)=>s.Ay.post("api/menu/get-all",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteMenu:(e,t)=>s.Ay.delete("api/menu/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}})}},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(74556),a=r(49509);let o=s.Ik({NEXT_PUBLIC_API_ENDPOINT:s.Yj().url(),NEXT_PUBLIC_URL:s.Yj().url(),CRYPTOJS_SECRECT:s.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!o.success)throw console.error("Invalid environment variables:",o.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let n=o.data},99843:(e,t,r)=>{"use strict";r.d(t,{SettingProvider:()=>d,i:()=>m});var s=r(95155),a=r(12115),o=r(83931),n=r(35695);let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{let r=localStorage.getItem("".concat(e,"_timestamp"));if(!r)return!0;let s=parseInt(r);return Date.now()-s>60*t*1e3}catch(e){return console.error("❌ Error checking cache staleness:",e),!0}},l=(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t)),localStorage.setItem("".concat(e,"_timestamp"),Date.now().toString())}catch(e){console.error("❌ Error setting cache with timestamp:",e)}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{if(i(e,t))return null;let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return console.error("❌ Error getting fresh cache:",e),null}},u=(0,a.createContext)(void 0),d=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[d,m]=(0,a.useState)(null),[h,p]=(0,a.useState)(!0),g=(0,n.usePathname)(),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(p(!0),!e){let e=c("siteSetting",5),t=c("siteMenus",5);if(e&&t){i(e),m(t),p(!1);return}}let t=await o.A.commonFetchSetting();t.payload.success?(i(t.payload.setting),m(t.payload.menus),l("siteSetting",t.payload.setting),l("siteMenus",t.payload.menus)):console.error("Failed to fetch settings")}catch(e){console.error("Error fetching settings:",e)}finally{p(!1)}};return(0,a.useEffect)(()=>{if("/"===g||g.startsWith("/dashboard"))f();else{let e=c("siteSetting",30),t=c("siteMenus",30);e&&t?(i(e),m(t),p(!1)):f()}},[g]),(0,s.jsx)(u.Provider,{value:{setting:r,loading:h,menus:d,refreshSettings:()=>{console.log("\uD83D\uDD04 Force refreshing settings...");try{localStorage.removeItem("siteSetting"),localStorage.removeItem("siteMenus"),console.log("✅ Setting cache cleared")}catch(e){console.error("❌ Error clearing setting cache:",e)}f(!0)}},children:t})},m=()=>{let e=(0,a.useContext)(u);if(!e)throw Error("useSetting must be used within a SettingProvider");return e}}},e=>{e.O(0,[9268,3235,8543,2182,6874,716,2509,8441,5964,7358],()=>e(e.s=14019)),_N_E=e.O()}]);