(()=>{var a={};a.id=6156,a.ids=[6156],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7321:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(private)",{children:["dashboard",{children:["files",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,30070)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\files\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,75582)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\files\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/files/page",pathname:"/dashboard/files",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(private)/dashboard/files/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30070:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tand\\\\src\\\\app\\\\(private)\\\\dashboard\\\\files\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tand\\src\\app\\(private)\\dashboard\\files\\page.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},36294:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(60687),e=c(43210),f=c(93853),g=c(9113);let h={getFiles:(a,b)=>g.Ay.post("/api/administrator/files",a,{headers:{Authorization:`Bearer ${b}`}}),deleteFile:(a,b)=>g.Ay.delete(`/api/administrator/files/${a}`,{headers:{Authorization:`Bearer ${b}`}}),bulkAction:(a,b)=>g.Ay.post("/api/administrator/files/bulk-action",a,{headers:{Authorization:`Bearer ${b}`}}),getFileStats:a=>g.Ay.get("/api/administrator/files/stats",{headers:{Authorization:`Bearer ${a}`}}),getSyncStatus:a=>g.Ay.get("/api/administrator/files/sync-status",{headers:{Authorization:`Bearer ${a}`}}),syncExistingFiles:a=>g.Ay.post("/api/administrator/files/sync-existing",{},{headers:{Authorization:`Bearer ${a}`}})};var i=c(66800);let j=({files:a,onFileSelect:b,onFileDelete:c,onBulkAction:g,loading:h=!1})=>{let[j,k]=(0,e.useState)([]),[l,m]=(0,e.useState)(!1);(0,e.useEffect)(()=>{m(j.length===a.length&&a.length>0)},[j,a]);let n=a=>{if(0===j.length)return void f.oR.warning("Please select files first");confirm("delete"===a?`Are you sure you want to delete ${j.length} files?`:`Are you sure you want to ${a} ${j.length} files?`)&&(g(j,a),k([]),m(!1))};return h?(0,d.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"ml-2",children:"Loading files..."})]}):0===a.length?(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCC1"}),(0,d.jsx)("p",{children:"No files found"})]}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[j.length>0&&(0,d.jsxs)("div",{className:"bg-blue-50 border-b px-4 py-3 flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm text-blue-700",children:[j.length," files selected"]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>n("activate"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Activate"}),(0,d.jsx)("button",{onClick:()=>n("deactivate"),className:"px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700",children:"Deactivate"}),(0,d.jsx)("button",{onClick:()=>n("delete"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"Delete"})]})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-3 text-left",children:(0,d.jsx)("input",{type:"checkbox",checked:l,onChange:()=>{l?k([]):k(a.map(a=>a._id)),m(!l)},className:"rounded border-gray-300"})}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded By"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(a=>(0,d.jsxs)("tr",{className:`hover:bg-gray-50 ${j.includes(a._id)?"bg-blue-50":""}`,children:[(0,d.jsx)("td",{className:"px-4 py-3",children:(0,d.jsx)("input",{type:"checkbox",checked:j.includes(a._id),onChange:()=>(a=>{k(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])})(a._id),className:"rounded border-gray-300"})}),(0,d.jsx)("td",{className:"px-4 py-3",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:((a,b)=>{switch(a){case"image":return"\uD83D\uDDBC️";case"video":return"\uD83C\uDFAC";case"document":if(b.includes("pdf"))return"\uD83D\uDCC4";if(b.includes("word"))return"\uD83D\uDCDD";if(b.includes("excel"))return"\uD83D\uDCCA";return"\uD83D\uDCC4";default:return"\uD83D\uDCC1"}})(a.type,a.mimetype)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate max-w-xs",children:a.originalName}),(0,d.jsx)("div",{className:"text-xs text-gray-500 truncate max-w-xs",children:a.filename})]})]})}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,d.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:a.type})}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,i.z3)(a.size)}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:a.uploadedBy.username}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,i.Yq)(a.uploadedAt)}),(0,d.jsx)("td",{className:"px-4 py-3",children:(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${a.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:a.isActive?"Active":"Inactive"})}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>b(a),className:"text-blue-600 hover:text-blue-900",children:"View"}),(0,d.jsx)("button",{onClick:()=>window.open(a.url,"_blank"),className:"text-green-600 hover:text-green-900",children:"Download"}),(0,d.jsx)("button",{onClick:()=>c(a._id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},a._id))})]})})]})},k=({onSyncComplete:a})=>{let[b,c]=(0,e.useState)(null),[g,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),m=async()=>{try{let a=localStorage.getItem("sessionToken")||"",b=await h.getSyncStatus(a);b.payload.success&&c(b.payload.status)}catch(a){console.error("Error fetching sync status:",a)}},n=async()=>{if(!b||0===b.missingInDatabase)return void f.oR.info("Kh\xf4ng c\xf3 file n\xe0o cần đồng bộ");if(confirm(`Bạn c\xf3 chắc muốn đồng bộ ${b.missingInDatabase} files? Qu\xe1 tr\xecnh n\xe0y c\xf3 thể mất một \xedt thời gian.`))try{j(!0);let b=localStorage.getItem("sessionToken")||"";(await h.syncExistingFiles(b)).payload.success?(f.oR.success("Đ\xe3 bắt đầu đồng bộ file. Vui l\xf2ng kiểm tra lại sau v\xe0i ph\xfat."),setTimeout(()=>{m(),a?.()},3e3)):f.oR.error("Kh\xf4ng thể bắt đầu đồng bộ file")}catch(a){console.error("Error syncing files:",a),f.oR.error("Đ\xe3 xảy ra lỗi khi đồng bộ file")}finally{j(!1)}};if((0,e.useEffect)(()=>{m();let a=setInterval(m,3e4);return()=>clearInterval(a)},[]),!b)return(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,d.jsx)("div",{className:"h-6 bg-gray-300 rounded w-1/2"})]})});let o=0===b.missingInDatabase;return(0,d.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-2xl mr-3",children:g?"\uD83D\uDD04":o?"✅":"⚠️"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"File Sync Status"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Trạng th\xe1i đồng bộ giữa thư mục vật l\xfd v\xe0 database"})]})]}),(0,d.jsx)("button",{onClick:()=>l(!k),className:"text-blue-600 hover:text-blue-800 text-sm",children:k?"Ẩn chi tiết":"Xem chi tiết"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:b.totalPhysicalFiles}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Files vật l\xfd"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:b.totalDatabaseFiles}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Files trong DB"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,d.jsx)("div",{className:`text-2xl font-bold ${b.missingInDatabase>0?"text-orange-600":"text-green-600"}`,children:b.missingInDatabase}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Chưa đồng bộ"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,d.jsx)("div",{className:`text-2xl font-bold ${o?"text-green-600":"text-orange-600"}`,children:o?"100%":Math.round(b.totalDatabaseFiles/b.totalPhysicalFiles*100)+"%"}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Tỉ lệ sync"})]})]}),(0,d.jsx)("div",{className:`p-3 rounded-md mb-4 ${o?"bg-green-50 text-green-800":"bg-orange-50 text-orange-800"}`,children:g?(0,d.jsxs)("span",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}),"Đang đồng bộ files..."]}):o?"✅ Tất cả files đ\xe3 được đồng bộ":`⚠️ C\xf3 ${b.missingInDatabase} files chưa được đồng bộ v\xe0o database`}),!o&&!g&&(0,d.jsxs)("button",{onClick:n,disabled:g,className:"w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50",children:["\uD83D\uDD04 Đồng bộ ",b.missingInDatabase," files"]}),k&&(0,d.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Files chưa đồng bộ (10 đầu ti\xean):"}),b.missingFiles&&b.missingFiles.length>0?(0,d.jsxs)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[b.missingFiles.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded text-sm",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"truncate font-medium text-gray-900",children:a.filename}),(0,d.jsx)("div",{className:"text-gray-500 text-xs",children:a.relativePath})]}),(0,d.jsx)("div",{className:"ml-4 text-gray-600 text-xs",children:(0,i.z3)(a.size)})]},b)),b.missingInDatabase>b.missingFiles.length&&(0,d.jsxs)("div",{className:"text-center text-gray-500 text-sm py-2",children:["... v\xe0 ",b.missingInDatabase-b.missingFiles.length," files kh\xe1c"]})]}):(0,d.jsx)("div",{className:"text-gray-500 text-sm",children:"Kh\xf4ng c\xf3 files n\xe0o cần đồng bộ"})]}),(0,d.jsxs)("div",{className:"mt-4 flex justify-between items-center text-sm text-gray-500",children:[(0,d.jsxs)("span",{children:["Cập nhật lần cuối: ",new Date().toLocaleTimeString()]}),(0,d.jsx)("button",{onClick:m,className:"text-blue-600 hover:text-blue-800",children:"\uD83D\uDD04 L\xe0m mới"})]})]})};var l=c(98462);let m=()=>{let[a,b]=(0,e.useState)([]),[c,g]=(0,e.useState)(!0),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)({page:1,perPage:20,type:"all",sortBy:"uploadedAt",sortOrder:"desc"}),[q,r]=(0,e.useState)(0),[s,t]=(0,e.useState)(null),[u,v]=(0,e.useState)(!1),w=async()=>{try{g(!0);let a=localStorage.getItem("sessionToken")||"",c=await h.getFiles(o,a);c.payload.success?(b(c.payload.files),r(c.payload.total||0)):f.oR.error("Failed to fetch files")}catch(a){console.error("Error fetching files:",a),f.oR.error("An error occurred while fetching files")}finally{g(!1)}},x=async()=>{try{let a=localStorage.getItem("sessionToken")||"",b=await h.getFileStats(a);b.payload.success&&n(b.payload.stats)}catch(a){console.error("Error fetching stats:",a)}},y=async a=>{if(confirm("Are you sure you want to delete this file?"))try{let b=localStorage.getItem("sessionToken")||"";(await h.deleteFile(a,b)).payload.success?(f.oR.success("File deleted successfully"),w(),x()):f.oR.error("Failed to delete file")}catch(a){console.error("Error deleting file:",a),f.oR.error("An error occurred while deleting file")}},z=async(a,b)=>{try{let c=localStorage.getItem("sessionToken")||"";(await h.bulkAction({fileIds:a,action:b},c)).payload.success?(f.oR.success(`${b} completed successfully`),w(),x()):f.oR.error(`Failed to ${b} files`)}catch(a){console.error(`Error in bulk ${b}:`,a),f.oR.error(`An error occurred while performing ${b}`)}},A=(a,b)=>{p(c=>({...c,[a]:b,page:1}))},B=a=>{p(b=>({...b,page:a}))};(0,e.useEffect)(()=>{w()},[o]),(0,e.useEffect)(()=>{x()},[]);let C=Math.ceil(q/o.perPage);return(0,d.jsx)(l.default,{requiredPermissions:["file_view","file_upload","file_delete"],requireAll:!1,children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"File Management"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage uploaded files and media"})]}),m&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Total Files"}),(0,d.jsx)("p",{className:"text-xl font-semibold",children:(0,i.ZV)(m.totalFiles)})]})]})}),(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Total Size"}),(0,d.jsx)("p",{className:"text-xl font-semibold",children:(0,i.z3)(m.totalSize)})]})]})}),(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC8"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Recent Uploads"}),(0,d.jsx)("p",{className:"text-xl font-semibold",children:(0,i.ZV)(m.recentUploads)})]})]})}),(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"text-2xl mr-3",children:"\uD83C\uDFAF"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"File Types"}),(0,d.jsx)("p",{className:"text-xl font-semibold",children:m.filesByType.length})]})]})})]}),(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)(k,{onSyncComplete:()=>{w(),x()}})}),(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,d.jsx)("div",{className:"flex-1 min-w-64",children:(0,d.jsx)("input",{type:"text",placeholder:"Search files...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:a=>{var b;return b=a.target.value,void p(a=>({...a,query:b,page:1}))}})}),(0,d.jsxs)("select",{value:o.type,onChange:a=>A("type",a.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"All Types"}),(0,d.jsx)("option",{value:"image",children:"Images"}),(0,d.jsx)("option",{value:"video",children:"Videos"}),(0,d.jsx)("option",{value:"document",children:"Documents"}),(0,d.jsx)("option",{value:"other",children:"Other"})]}),(0,d.jsxs)("select",{value:`${o.sortBy}-${o.sortOrder}`,onChange:a=>{let[b,c]=a.target.value.split("-");A("sortBy",b),A("sortOrder",c)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"uploadedAt-desc",children:"Newest First"}),(0,d.jsx)("option",{value:"uploadedAt-asc",children:"Oldest First"}),(0,d.jsx)("option",{value:"filename-asc",children:"Name A-Z"}),(0,d.jsx)("option",{value:"filename-desc",children:"Name Z-A"}),(0,d.jsx)("option",{value:"size-desc",children:"Largest First"}),(0,d.jsx)("option",{value:"size-asc",children:"Smallest First"})]}),(0,d.jsx)("button",{onClick:()=>v(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"\uD83D\uDCE4 Upload File"})]})}),(0,d.jsx)(j,{files:a,onFileSelect:t,onFileDelete:y,onBulkAction:z,loading:c}),C>1&&(0,d.jsx)("div",{className:"mt-6 flex justify-center",children:(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>B(o.page-1),disabled:o.page<=1,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:Math.min(5,C)},(a,b)=>{let c=b+1;return(0,d.jsx)("button",{onClick:()=>B(c),className:`px-3 py-2 border rounded-md ${o.page===c?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"}`,children:c},c)}),(0,d.jsx)("button",{onClick:()=>B(o.page+1),disabled:o.page>=C,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})}),s&&(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsxs)("div",{className:"p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",style:{color:"#111827"},children:"File Details"}),(0,d.jsx)("button",{onClick:()=>t(null),className:"text-xl font-bold p-1 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("div",{className:"space-y-3",style:{color:"#111827"},children:[(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Name:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.originalName})]}),(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Type:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.type})]}),(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Size:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:(0,i.z3)(s.size)})]}),(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Uploaded by:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.uploadedBy.username})]}),(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Upload date:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:new Date(s.uploadedAt).toLocaleString()})]}),(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Status:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.isActive?"Active":"Inactive"})]}),s.description&&(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Description:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.description})]}),s.tags&&s.tags.length>0&&(0,d.jsxs)("div",{style:{color:"#111827"},children:[(0,d.jsx)("strong",{style:{color:"#111827"},children:"Tags:"}),(0,d.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:s.tags.join(", ")})]})]}),(0,d.jsxs)("div",{className:"mt-6 flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>window.open(s.url,"_blank"),className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:"View/Download"}),(0,d.jsx)("button",{onClick:()=>{y(s._id),t(null)},className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"Delete"})]})]})})]})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46856:(a,b,c)=>{Promise.resolve().then(c.bind(c,36294))},55511:a=>{"use strict";a.exports=require("crypto")},60008:(a,b,c)=>{Promise.resolve().then(c.bind(c,30070))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66800:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>e,ZV:()=>f,z3:()=>d});let d=(a,b=2)=>{if(0===a)return"0 Bytes";let c=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,c)).toFixed(b<0?0:b))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][c]},e=a=>{if(!a)return"";let b=new Date(a);return isNaN(b.getTime())?"":b.toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit"})},f=a=>a.toLocaleString()},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},98462:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(55109),f=c(16189);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isLoading:l}=(0,e.S)(),m=(0,f.useRouter)();if(l)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(43210)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,8256,9008,3581,1178],()=>b(b.s=7321));module.exports=c})();