import React from 'react';
import PermissionGuard from '@/components/PermissionGuard';

interface Params {
  id: string;
  memberId: string;
}

export default function DepartmentMemberPage({ params }: { params: Params }) {
  return (
    <PermissionGuard requiredPermission="admin">
      <div className="content">
        <div className="max-w-4xl mx-auto p-6">
          <h1 className="text-2xl font-bold mb-6">Thông tin thành viên</h1>
          <div className="bg-white rounded-lg shadow p-6">
            <p>Department ID: {params.id}</p>
            <p>Member ID: {params.memberId}</p>
            <p className="text-yellow-600">
              Trang thông tin thành viên đang được phát triển...
            </p>
            <div className="mt-4 space-x-2">
              <a 
                href={`/dashboard/departments/${params.id}/members/${params.memberId}/edit`}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Chỉnh sửa
              </a>
              <a 
                href={`/dashboard/departments/${params.id}`}
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
              >
                Quay lại phòng ban
              </a>
            </div>
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
