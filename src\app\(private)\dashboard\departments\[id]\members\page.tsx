import React from 'react';
import PermissionGuard from '@/components/PermissionGuard';

interface Params {
  id: string;
}

export default function DepartmentMembersPage({ params }: { params: Params }) {
  return (
    <PermissionGuard requiredPermission="admin">
      <div className="content">
        <div className="max-w-6xl mx-auto p-6">
          <h1 className="text-2xl font-bold mb-6">Danh sách thành viên phòng ban</h1>
          <div className="bg-white rounded-lg shadow p-6">
            <p>Department ID: {params.id}</p>
            <p className="text-yellow-600">
              Danh sách thành viên phòng ban đang được phát triển...
            </p>
            <div className="mt-4 space-x-2">
              <a 
                href={`/dashboard/departments/${params.id}/members/add`}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                Th<PERSON><PERSON> thành viên
              </a>
              <a 
                href={`/dashboard/departments/${params.id}`}
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
              >
                Quay lại phòng ban
              </a>
            </div>
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
