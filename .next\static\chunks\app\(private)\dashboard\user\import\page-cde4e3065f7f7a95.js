(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[286],{738:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var i=r(95155),s=r(12115),n=r(60408),a=r.n(n),o=r(38543);r(85716);var l=r(11725);let h=()=>{let[e,t]=(0,s.useState)(null),[r,n]=(0,s.useState)(!1),[h,u]=(0,s.useState)([]),[d,c]=(0,s.useState)(0),[f,p]=(0,s.useState)(0),[m,g]=(0,s.useState)(0),[_,y]=(0,s.useState)([]),b=async()=>{if(0===h.length)return void o.oR.warn("No data to upload!");n(!0),y([]),p(0),g(0);let e=0,t=0,r=Math.min(h.length,100);for(let i=0;i<r;i++){let s="";try{let r=localStorage.getItem("sessionToken")||"",n=await l.A.CreateUser(h[i],r);n.payload.success?(e++,s="Success"):(t++,s="Error: ".concat(n.payload.message||"Unknown error"))}catch(e){t++,s=e.payload.message}p(e),g(t),c(Math.round((i+1)/r*100)),y(e=>[...e,{...h[i],status:s}]),i<r-1&&await new Promise(e=>setTimeout(e,2e3))}n(!1),o.oR.success("Import completed!")};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"mt-4 p-4 border rounded border-dashed",children:(0,i.jsx)("input",{type:"file",accept:".csv",onChange:e=>{var r;(null==(r=e.target.files)?void 0:r[0])&&t(e.target.files[0])}})}),(0,i.jsxs)("div",{className:"my-4 flex gap-4",children:[(0,i.jsx)("button",{className:"btn btn-md",onClick:()=>{e&&a().parse(e,{header:!0,skipEmptyLines:!0,complete:e=>{if(0===e.data.length)return void o.oR.error("No valid data found in the file!");let t=[],r=0;e.data.forEach(e=>{e.username&&e.email&&e.password?t.length<100&&t.push(e):r++}),u(t),p(0),g(r),y([]),o.oR.success("Ph\xe2n t\xedch: ".concat(t.length," hợp lệ, ").concat(r," kh\xf4ng hợp lệ"))},error:()=>{o.oR.error("Failed to parse CSV file!")}})},disabled:!e,children:"Kiểm tra File"}),(0,i.jsx)("button",{className:"btn btn-md btn-primary",onClick:b,disabled:r||0===h.length,children:r?"Đang tải... ".concat(d,"%"):"Import Dữ Liệu"})]}),(f+m>0||_.length>0)&&(0,i.jsxs)("div",{className:"mt-4 border p-4 rounded bg-gray-100",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Kết quả Import"}),(0,i.jsxs)("p",{children:["Dữ liệu th\xe0nh c\xf4ng:"," ",(0,i.jsx)("span",{className:"text-green-600 font-bold",children:f})]}),(0,i.jsxs)("p",{children:["Dữ liệu lỗi:"," ",(0,i.jsx)("span",{className:"text-red-600 font-bold",children:m})]}),(0,i.jsxs)("p",{children:["Tiến độ:"," ",(0,i.jsxs)("span",{className:"text-blue-600 font-bold",children:[d,"%"]})]})]}),_.length>0&&(0,i.jsx)("div",{className:"mt-6",children:(0,i.jsxs)("table",{className:"table-auto w-full border border-collapse mt-4",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"bg-gray-200",children:[(0,i.jsx)("th",{className:"border px-2 py-1",children:"Username"}),(0,i.jsx)("th",{className:"border px-2 py-1",children:"Email"}),(0,i.jsx)("th",{className:"border px-2 py-1",children:"Phone Number"}),(0,i.jsx)("th",{className:"border px-2 py-1",children:"Status"})]})}),(0,i.jsx)("tbody",{children:_.map((e,t)=>(0,i.jsxs)("tr",{className:"border",children:[(0,i.jsx)("td",{className:"border px-2 py-1",children:e.username}),(0,i.jsx)("td",{className:"border px-2 py-1",children:e.email}),(0,i.jsx)("td",{className:"border px-2 py-1",children:e.phonenumber}),(0,i.jsx)("td",{className:"border px-2 py-1 ".concat(e.status.includes("Success")?"text-green-600":"text-red-600"),children:e.status})]},t))})]})})]})}},11725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(27937);let s={fetchUsers:(e,t)=>i.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>i.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>i.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>i.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>i.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>i.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>i.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>i.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},15092:(e,t,r)=>{Promise.resolve().then(r.bind(r,738)),Promise.resolve().then(r.bind(r,87708))},23348:(e,t,r)=>{"use strict";r.d(t,{U:()=>a,default:()=>o});var i=r(95155),s=r(12115);let n=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),a=()=>(0,s.useContext)(n),o=e=>{let{children:t}=e,[r,a]=(0,s.useState)(()=>null),[o,l]=(0,s.useState)(!0),h=(0,s.useCallback)(e=>{a(e),localStorage.setItem("user",JSON.stringify(e))},[a]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");a(e?JSON.parse(e):null),l(!1)},[a]),(0,i.jsx)(n.Provider,{value:{user:r,setUser:h,isAuthenticated:!!r,isLoading:o},children:t})}},27937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var i=r(84559),s=r(59434),n=r(35695);class a extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class o extends a{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,h=async(e,t,r)=>{let h;(null==r?void 0:r.body)instanceof FormData?h=r.body:(null==r?void 0:r.body)&&(h=JSON.stringify(r.body));let u=h instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?i.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,c=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),f=await fetch(c,{...r,headers:{...u,...null==r?void 0:r.headers},body:h,method:e}),p=null,m=f.headers.get("content-type");if(m&&m.includes("application/json"))try{p=await f.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await f.text();let g={status:f.status,payload:p};if(!f.ok)if(404===f.status||403===f.status)throw new o(g);else if(401===f.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new a(g);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return g},u={get:(e,t)=>h("GET",e,t),post:(e,t,r)=>h("POST",e,{...r,body:t}),put:(e,t,r)=>h("PUT",e,{...r,body:t}),patch:(e,t,r)=>h("PATCH",e,{...r,body:t}),delete:(e,t)=>h("DELETE",e,{...t})}},38497:(e,t,r)=>{"use strict";r.d(t,{S:()=>s});var i=r(23348);let s=()=>{let{user:e,isLoading:t}=(0,i.U)();return{hasPermission:r=>{var i;return!t&&!!e&&("admin"===e.rule||(null==(i=e.permissions)?void 0:i.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isLoading:t}}},59434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>a,cn:()=>n}),r(27937);var i=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,i.$)(t))}r(58801);let a=e=>e.startsWith("/")?e.slice(1):e},60408:function(e,t){var r,i;void 0===(i="function"==typeof(r=function e(){var t,r="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r?r:{},i=!r.document&&!!r.postMessage,s=r.IS_PAPA_WORKER||!1,n={},a=0,o={};function l(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(e){var t=b(e);t.chunkSize=parseInt(t.chunkSize),e.step||e.chunk||(t.chunkSize=null),this._handle=new f(t),(this._handle.streamer=this)._config=t}).call(this,e),this.parseChunk=function(e,t){var i=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<i){let t=this._config.newline;t||(n=this._config.quoteChar||'"',t=this._handle.guessLineEndings(e,n)),e=[...e.split(t).slice(i)].join(t)}this.isFirstChunk&&k(this._config.beforeFirstChunk)&&void 0!==(n=this._config.beforeFirstChunk(e))&&(e=n),this.isFirstChunk=!1,this._halted=!1;var i=this._partialLine+e,n=(this._partialLine="",this._handle.parse(i,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(e=n.meta.cursor,this._finished||(this._partialLine=i.substring(e-this._baseIndex),this._baseIndex=e),n&&n.data&&(this._rowCount+=n.data.length),i=this._finished||this._config.preview&&this._rowCount>=this._config.preview,s)r.postMessage({results:n,workerId:o.WORKER_ID,finished:i});else if(k(this._config.chunk)&&!t){if(this._config.chunk(n,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=n=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(n.data),this._completeResults.errors=this._completeResults.errors.concat(n.errors),this._completeResults.meta=n.meta),this._completed||!i||!k(this._config.complete)||n&&n.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),i||n&&n.meta.paused||this._nextChunk(),n}this._halted=!0},this._sendError=function(e){k(this._config.error)?this._config.error(e):s&&this._config.error&&r.postMessage({workerId:o.WORKER_ID,error:e,finished:!1})}}function h(e){var t;(e=e||{}).chunkSize||(e.chunkSize=o.RemoteChunkSize),l.call(this,e),this._nextChunk=i?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(e){this._input=e,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),i||(t.onload=v(this._chunkLoaded,this),t.onerror=v(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!i),this._config.downloadRequestHeaders){var e,r,s=this._config.downloadRequestHeaders;for(r in s)t.setRequestHeader(r,s[r])}this._config.chunkSize&&(e=this._start+this._config.chunkSize-1,t.setRequestHeader("Range","bytes="+this._start+"-"+e));try{t.send(this._config.downloadRequestBody)}catch(e){this._chunkError(e.message)}i&&0===t.status&&this._chunkError()}},this._chunkLoaded=function(){let e;4===t.readyState&&(t.status<200||400<=t.status?this._chunkError():(this._start+=this._config.chunkSize||t.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null!==(e=(e=t).getResponseHeader("Content-Range"))?parseInt(e.substring(e.lastIndexOf("/")+1)):-1),this.parseChunk(t.responseText)))},this._chunkError=function(e){e=t.statusText||e,this._sendError(Error(e))}}function u(e){(e=e||{}).chunkSize||(e.chunkSize=o.LocalChunkSize),l.call(this,e);var t,r,i="undefined"!=typeof FileReader;this.stream=function(e){this._input=e,r=e.slice||e.webkitSlice||e.mozSlice,i?((t=new FileReader).onload=v(this._chunkLoaded,this),t.onerror=v(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var e=this._input,s=(this._config.chunkSize&&(s=Math.min(this._start+this._config.chunkSize,this._input.size),e=r.call(e,this._start,s)),t.readAsText(e,this._config.encoding));i||this._chunkLoaded({target:{result:s}})},this._chunkLoaded=function(e){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(e.target.result)},this._chunkError=function(){this._sendError(t.error)}}function d(e){var t;l.call(this,e=e||{}),this.stream=function(e){return t=e,this._nextChunk()},this._nextChunk=function(){var e,r;if(!this._finished)return t=(e=this._config.chunkSize)?(r=t.substring(0,e),t.substring(e)):(r=t,""),this._finished=!t,this.parseChunk(r)}}function c(e){l.call(this,e=e||{});var t=[],r=!0,i=!1;this.pause=function(){l.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){l.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(e){this._input=e,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){i&&1===t.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):r=!0},this._streamData=v(function(e){try{t.push("string"==typeof e?e:e.toString(this._config.encoding)),r&&(r=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(e){this._streamError(e)}},this),this._streamError=v(function(e){this._streamCleanUp(),this._sendError(e)},this),this._streamEnd=v(function(){this._streamCleanUp(),i=!0,this._streamData("")},this),this._streamCleanUp=v(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function f(e){var t,r,i,s,n=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,a=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,l=this,h=0,u=0,d=!1,c=!1,f=[],g={data:[],errors:[],meta:{}};function _(t){return"greedy"===e.skipEmptyLines?""===t.join("").trim():1===t.length&&0===t[0].length}function y(){if(g&&i&&(x("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),i=!1),e.skipEmptyLines&&(g.data=g.data.filter(function(e){return!_(e)})),v()){if(g)if(Array.isArray(g.data[0])){for(var t,r=0;v()&&r<g.data.length;r++)g.data[r].forEach(s);g.data.splice(0,1)}else g.data.forEach(s);function s(t,r){k(e.transformHeader)&&(t=e.transformHeader(t,r)),f.push(t)}}function l(t,r){for(var i=e.header?{}:[],s=0;s<t.length;s++){var o=s,l=t[s],l=((t,r)=>(e.dynamicTypingFunction&&void 0===e.dynamicTyping[t]&&(e.dynamicTyping[t]=e.dynamicTypingFunction(t)),!0===(e.dynamicTyping[t]||e.dynamicTyping))?"true"===r||"TRUE"===r||"false"!==r&&"FALSE"!==r&&((e=>{if(n.test(e)&&-0x20000000000000<(e=parseFloat(e))&&e<0x20000000000000)return 1})(r)?parseFloat(r):a.test(r)?new Date(r):""===r?null:r):r)(o=e.header?s>=f.length?"__parsed_extra":f[s]:o,l=e.transform?e.transform(l,o):l);"__parsed_extra"===o?(i[o]=i[o]||[],i[o].push(l)):i[o]=l}return e.header&&(s>f.length?x("FieldMismatch","TooManyFields","Too many fields: expected "+f.length+" fields but parsed "+s,u+r):s<f.length&&x("FieldMismatch","TooFewFields","Too few fields: expected "+f.length+" fields but parsed "+s,u+r)),i}g&&(e.header||e.dynamicTyping||e.transform)&&(t=1,!g.data.length||Array.isArray(g.data[0])?(g.data=g.data.map(l),t=g.data.length):g.data=l(g.data,0),e.header&&g.meta&&(g.meta.fields=f),u+=t)}function v(){return e.header&&0===f.length}function x(e,t,r,i){e={type:e,code:t,message:r},void 0!==i&&(e.row=i),g.errors.push(e)}k(e.step)&&(s=e.step,e.step=function(t){g=t,v()?y():(y(),0!==g.data.length&&(h+=t.data.length,e.preview&&h>e.preview?r.abort():(g.data=g.data[0],s(g,l))))}),this.parse=function(s,n,a){var l=e.quoteChar||'"',l=(e.newline||(e.newline=this.guessLineEndings(s,l)),i=!1,e.delimiter?k(e.delimiter)&&(e.delimiter=e.delimiter(s),g.meta.delimiter=e.delimiter):((l=((t,r,i,s,n)=>{var a,l,h,u;n=n||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var d=0;d<n.length;d++){for(var c,f=n[d],p=0,g=0,y=0,b=(h=void 0,new m({comments:s,delimiter:f,newline:r,preview:10}).parse(t)),v=0;v<b.data.length;v++)i&&_(b.data[v])?y++:(g+=c=b.data[v].length,void 0===h?h=c:0<c&&(p+=Math.abs(c-h),h=c));0<b.data.length&&(g/=b.data.length-y),(void 0===l||p<=l)&&(void 0===u||u<g)&&1.99<g&&(l=p,a=f,u=g)}return{successful:!!(e.delimiter=a),bestDelimiter:a}})(s,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess)).successful?e.delimiter=l.bestDelimiter:(i=!0,e.delimiter=o.DefaultDelimiter),g.meta.delimiter=e.delimiter),b(e));return e.preview&&e.header&&l.preview++,t=s,g=(r=new m(l)).parse(t,n,a),y(),d?{meta:{paused:!0}}:g||{meta:{paused:!1}}},this.paused=function(){return d},this.pause=function(){d=!0,r.abort(),t=k(e.chunk)?"":t.substring(r.getCharIndex())},this.resume=function(){l.streamer._halted?(d=!1,l.streamer.parseChunk(t,!0)):setTimeout(l.resume,3)},this.aborted=function(){return c},this.abort=function(){c=!0,r.abort(),g.meta.aborted=!0,k(e.complete)&&e.complete(g),t=""},this.guessLineEndings=function(e,t){e=e.substring(0,1048576);var t=RegExp(p(t)+"([^]*?)"+p(t),"gm"),r=(e=e.replace(t,"")).split("\r"),t=e.split("\n"),e=1<t.length&&t[0].length<r[0].length;if(1===r.length||e)return"\n";for(var i=0,s=0;s<r.length;s++)"\n"===r[s][0]&&i++;return i>=r.length/2?"\r\n":"\r"}}function p(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function m(e){var t=(e=e||{}).delimiter,r=e.newline,i=e.comments,s=e.step,n=e.preview,a=e.fastMode,l=null,h=!1,u=null==e.quoteChar?'"':e.quoteChar,d=u;if(void 0!==e.escapeChar&&(d=e.escapeChar),("string"!=typeof t||-1<o.BAD_DELIMITERS.indexOf(t))&&(t=","),i===t)throw Error("Comment character same as delimiter");!0===i?i="#":("string"!=typeof i||-1<o.BAD_DELIMITERS.indexOf(i))&&(i=!1),"\n"!==r&&"\r"!==r&&"\r\n"!==r&&(r="\n");var c=0,f=!1;this.parse=function(o,m,g){if("string"!=typeof o)throw Error("Input must be a string");var _=o.length,y=t.length,b=r.length,v=i.length,x=k(s),E=[],w=[],C=[],S=c=0;if(!o)return U();if(a||!1!==a&&-1===o.indexOf(u)){for(var R=o.split(r),A=0;A<R.length;A++){if(C=R[A],c+=C.length,A!==R.length-1)c+=r.length;else if(g)break;if(!i||C.substring(0,v)!==i){if(x){if(E=[],L(C.split(t)),z(),f)return U()}else L(C.split(t));if(n&&n<=A)return E=E.slice(0,n),U(!0)}}return U()}for(var O=o.indexOf(t,c),I=o.indexOf(r,c),T=RegExp(p(d)+p(u),"g"),j=o.indexOf(u,c);;)if(o[c]===u)for(j=c,c++;;){if(-1===(j=o.indexOf(u,j+1)))return g||w.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:E.length,index:c}),P();if(j===_-1)return P(o.substring(c,j).replace(T,u));if(u===d&&o[j+1]===d)j++;else if(u===d||0===j||o[j-1]!==d){-1!==O&&O<j+1&&(O=o.indexOf(t,j+1));var N=D(-1===(I=-1!==I&&I<j+1?o.indexOf(r,j+1):I)?O:Math.min(O,I));if(o.substr(j+1+N,y)===t){C.push(o.substring(c,j).replace(T,u)),o[c=j+1+N+y]!==u&&(j=o.indexOf(u,c)),O=o.indexOf(t,c),I=o.indexOf(r,c);break}if(N=D(I),o.substring(j+1+N,j+1+N+b)===r){if(C.push(o.substring(c,j).replace(T,u)),F(j+1+N+b),O=o.indexOf(t,c),j=o.indexOf(u,c),x&&(z(),f))return U();if(n&&E.length>=n)return U(!0);break}w.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:E.length,index:c}),j++}}else if(i&&0===C.length&&o.substring(c,c+v)===i){if(-1===I)return U();c=I+b,I=o.indexOf(r,c),O=o.indexOf(t,c)}else if(-1!==O&&(O<I||-1===I))C.push(o.substring(c,O)),c=O+y,O=o.indexOf(t,c);else{if(-1===I)break;if(C.push(o.substring(c,I)),F(I+b),x&&(z(),f))return U();if(n&&E.length>=n)return U(!0)}return P();function L(e){E.push(e),S=c}function D(e){return -1!==e&&(e=o.substring(j+1,e))&&""===e.trim()?e.length:0}function P(e){return g||(void 0===e&&(e=o.substring(c)),C.push(e),c=_,L(C),x&&z()),U()}function F(e){c=e,L(C),C=[],I=o.indexOf(r,c)}function U(i){if(e.header&&!m&&E.length&&!h){var s=E[0],n=Object.create(null),a=new Set(s);let t=!1;for(let r=0;r<s.length;r++){let i=s[r];if(n[i=k(e.transformHeader)?e.transformHeader(i,r):i]){let e,o=n[i];for(;e=i+"_"+o,o++,a.has(e););a.add(e),s[r]=e,n[i]++,t=!0,(l=null===l?{}:l)[e]=i}else n[i]=1,s[r]=i;a.add(i)}t&&console.warn("Duplicate headers found and renamed."),h=!0}return{data:E,errors:w,meta:{delimiter:t,linebreak:r,aborted:f,truncated:!!i,cursor:S+(m||0),renamedHeaders:l}}}function z(){s(U()),E=[],w=[]}},this.abort=function(){f=!0},this.getCharIndex=function(){return c}}function g(e){var t=e.data,r=n[t.workerId],i=!1;if(t.error)r.userError(t.error,t.file);else if(t.results&&t.results.data){var s={abort:function(){i=!0,_(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:y,resume:y};if(k(r.userStep)){for(var a=0;a<t.results.data.length&&(r.userStep({data:t.results.data[a],errors:t.results.errors,meta:t.results.meta},s),!i);a++);delete t.results}else k(r.userChunk)&&(r.userChunk(t.results,s,t.file),delete t.results)}t.finished&&!i&&_(t.workerId,t.results)}function _(e,t){var r=n[e];k(r.userComplete)&&r.userComplete(t),r.terminate(),delete n[e]}function y(){throw Error("Not implemented.")}function b(e){if("object"!=typeof e||null===e)return e;var t,r=Array.isArray(e)?[]:{};for(t in e)r[t]=b(e[t]);return r}function v(e,t){return function(){e.apply(t,arguments)}}function k(e){return"function"==typeof e}return o.parse=function(t,i){var s,l,f,p=(i=i||{}).dynamicTyping||!1;if(k(p)&&(i.dynamicTypingFunction=p,p={}),i.dynamicTyping=p,i.transform=!!k(i.transform)&&i.transform,!i.worker||!o.WORKERS_SUPPORTED){let e;return p=null,o.NODE_STREAM_INPUT,"string"==typeof t?(t=65279!==(e=t).charCodeAt(0)?e:e.slice(1),p=new(i.download?h:d)(i)):!0===t.readable&&k(t.read)&&k(t.on)?p=new c(i):(r.File&&t instanceof File||t instanceof Object)&&(p=new u(i)),p.stream(t)}(p=!!o.WORKERS_SUPPORTED&&(l=r.URL||r.webkitURL||null,f=e.toString(),s=o.BLOB_URL||(o.BLOB_URL=l.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",f,")();"],{type:"text/javascript"}))),(s=new r.Worker(s)).onmessage=g,s.id=a++,n[s.id]=s)).userStep=i.step,p.userChunk=i.chunk,p.userComplete=i.complete,p.userError=i.error,i.step=k(i.step),i.chunk=k(i.chunk),i.complete=k(i.complete),i.error=k(i.error),delete i.worker,p.postMessage({input:t,config:i,workerId:p.id})},o.unparse=function(e,t){var r=!1,i=!0,s=",",n="\r\n",a='"',l=a+a,h=!1,u=null,d=!1,c=((()=>{if("object"==typeof t){if("string"!=typeof t.delimiter||o.BAD_DELIMITERS.filter(function(e){return -1!==t.delimiter.indexOf(e)}).length||(s=t.delimiter),("boolean"==typeof t.quotes||"function"==typeof t.quotes||Array.isArray(t.quotes))&&(r=t.quotes),"boolean"!=typeof t.skipEmptyLines&&"string"!=typeof t.skipEmptyLines||(h=t.skipEmptyLines),"string"==typeof t.newline&&(n=t.newline),"string"==typeof t.quoteChar&&(a=t.quoteChar),"boolean"==typeof t.header&&(i=t.header),Array.isArray(t.columns)){if(0===t.columns.length)throw Error("Option columns is empty");u=t.columns}void 0!==t.escapeChar&&(l=t.escapeChar+a),t.escapeFormulae instanceof RegExp?d=t.escapeFormulae:"boolean"==typeof t.escapeFormulae&&t.escapeFormulae&&(d=/^[=+\-@\t\r].*$/)}})(),RegExp(p(a),"g"));if("string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return f(null,e,h);if("object"==typeof e[0])return f(u||Object.keys(e[0]),e,h)}else if("object"==typeof e)return"string"==typeof e.data&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields||u),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:"object"==typeof e.data[0]?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||"object"==typeof e.data[0]||(e.data=[e.data])),f(e.fields||[],e.data||[],h);throw Error("Unable to serialize unrecognized input");function f(e,t,r){var a="",o=("string"==typeof e&&(e=JSON.parse(e)),"string"==typeof t&&(t=JSON.parse(t)),Array.isArray(e)&&0<e.length),l=!Array.isArray(t[0]);if(o&&i){for(var h=0;h<e.length;h++)0<h&&(a+=s),a+=m(e[h],h);0<t.length&&(a+=n)}for(var u=0;u<t.length;u++){var d=(o?e:t[u]).length,c=!1,f=o?0===Object.keys(t[u]).length:0===t[u].length;if(r&&!o&&(c="greedy"===r?""===t[u].join("").trim():1===t[u].length&&0===t[u][0].length),"greedy"===r&&o){for(var p=[],g=0;g<d;g++){var _=l?e[g]:g;p.push(t[u][_])}c=""===p.join("").trim()}if(!c){for(var y=0;y<d;y++){0<y&&!f&&(a+=s);var b=o&&l?e[y]:y;a+=m(t[u][b],y)}u<t.length-1&&(!r||0<d&&!f)&&(a+=n)}}return a}function m(e,t){var i,n;return null==e?"":e.constructor===Date?JSON.stringify(e).slice(1,25):(n=!1,d&&"string"==typeof e&&d.test(e)&&(e="'"+e,n=!0),i=e.toString().replace(c,l),(n=n||!0===r||"function"==typeof r&&r(e,t)||Array.isArray(r)&&r[t]||((e,t)=>{for(var r=0;r<t.length;r++)if(-1<e.indexOf(t[r]))return!0;return!1})(i,o.BAD_DELIMITERS)||-1<i.indexOf(s)||" "===i.charAt(0)||" "===i.charAt(i.length-1))?a+i+a:i)}},o.RECORD_SEP="\x1e",o.UNIT_SEP="\x1f",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r","\n",'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!i&&!!r.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=0xa00000,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=m,o.ParserHandle=f,o.NetworkStreamer=h,o.FileStreamer=u,o.StringStreamer=d,o.ReadableStreamStreamer=c,r.jQuery&&((t=r.jQuery).fn.parse=function(e){var i=e.config||{},s=[];return this.each(function(e){if(!("INPUT"===t(this).prop("tagName").toUpperCase()&&"file"===t(this).attr("type").toLowerCase()&&r.FileReader)||!this.files||0===this.files.length)return!0;for(var n=0;n<this.files.length;n++)s.push({file:this.files[n],inputElem:this,instanceConfig:t.extend({},i)})}),n(),this;function n(){if(0===s.length)k(e.complete)&&e.complete();else{var r,i,n,l=s[0];if(k(e.before)){var h=e.before(l.file,l.inputElem);if("object"==typeof h){if("abort"===h.action)return r=l.file,i=l.inputElem,n=h.reason,void(k(e.error)&&e.error({name:"AbortError"},r,i,n));if("skip"===h.action)return void a();"object"==typeof h.config&&(l.instanceConfig=t.extend(l.instanceConfig,h.config))}else if("skip"===h)return void a()}var u=l.instanceConfig.complete;l.instanceConfig.complete=function(e){k(u)&&u(e,l.file,l.inputElem),a()},o.parse(l.file,l.instanceConfig)}}function a(){s.splice(0,1),n()}}),s&&(r.onmessage=function(e){e=e.data,void 0===o.WORKER_ID&&e&&(o.WORKER_ID=e.workerId),"string"==typeof e.input?r.postMessage({workerId:o.WORKER_ID,results:o.parse(e.input,e.config),finished:!0}):(r.File&&e.input instanceof File||e.input instanceof Object)&&(e=o.parse(e.input,e.config))&&r.postMessage({workerId:o.WORKER_ID,results:e,finished:!0})}),(h.prototype=Object.create(l.prototype)).constructor=h,(u.prototype=Object.create(l.prototype)).constructor=u,(d.prototype=Object.create(d.prototype)).constructor=d,(c.prototype=Object.create(l.prototype)).constructor=c,o})?r.apply(t,[]):r)||(e.exports=i)},84559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(74556),s=r(49509);let n=i.Ik({NEXT_PUBLIC_API_ENDPOINT:i.Yj().url(),NEXT_PUBLIC_URL:i.Yj().url(),CRYPTOJS_SECRECT:i.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let a=n.data},85716:()=>{},87708:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var i=r(95155),s=r(38497),n=r(35695),a=r(12115);function o(e){let{children:t,requiredPermission:r,requiredPermissions:o=[],requireAll:l=!1,fallbackPath:h="/dashboard"}=e,{hasPermission:u,hasAnyPermission:d,isAdmin:c,isLoading:f}=(0,s.S)(),p=(0,n.useRouter)();if((0,a.useEffect)(()=>{if(!f&&!c)(r?u(r):!(o.length>0)||(l?o.every(e=>u(e)):d(o)))||p.replace(h)},[u,d,c,f,r,o,l,h,p]),f)return(0,i.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(c)return(0,i.jsx)(i.Fragment,{children:t});return(r?u(r):!(o.length>0)||(l?o.every(e=>u(e)):d(o)))?(0,i.jsx)(i.Fragment,{children:t}):(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,i.jsx)("button",{onClick:()=>p.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}}},e=>{e.O(0,[4797,9268,3235,8543,8441,5964,7358],()=>e(e.s=15092)),_N_E=e.O()}]);