"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Dashboard<PERSON>ogo from "./Navigation/DashboardLogo";
import ButtonLogout from "./ui/button-logout";
import { useAuth } from "@/hooks/useAuth";
import { usePermissions } from "@/hooks/usePermissions";
import { useState, useEffect, useRef } from "react";
import { useSidebar } from "@/context/SidebarContext";
import {
  FileText,
  Users,
  Calendar,
  Settings,
  Folder,
  ChevronDown,
  ChevronRight,
  Globe,
  User,
  Shield,
  Upload,
  Menu,
  X,
  ChevronLeft
} from "react-feather";

export default function SideMenu() {
  const { hasPermission } = useAuth();
  const { hasPermission: hasFeaturePermission, isAdmin } = usePermissions();
  const { isCollapsed, isMobileOpen, toggleCollapse, closeMobileMenu } = useSidebar();
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith("/dashboard");
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const prevPathnameRef = useRef(pathname);

  // Auto close mobile menu when route changes (only when pathname actually changes)
  useEffect(() => {
    if (prevPathnameRef.current !== pathname && isMobileOpen) {
      closeMobileMenu();
    }
    prevPathnameRef.current = pathname;
  }, [pathname, closeMobileMenu, isMobileOpen]);

  // Debug mobile menu state
  useEffect(() => {
    console.log('SideMenu: isMobileOpen =', isMobileOpen, 'isCollapsed =', isCollapsed);
  }, [isMobileOpen, isCollapsed]);

  const toggleMenu = (href: string) => {
    if (isCollapsed) return; // Don't expand submenus when sidebar is collapsed
    setExpandedMenus(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const isMenuExpanded = (href: string) => !isCollapsed && expandedMenus.includes(href);
  const isActiveRoute = (href: string) => {
    // Exact match for dashboard root
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    // For other routes, check exact match or starts with route + '/'
    return pathname === href || pathname.startsWith(href + '/');
  };

  const MenuIcon_Component = ({ icon: Icon, className = "" }: { icon: any, className?: string }) => (
    <Icon size={18} className={className} />
  );
  // Build dynamic menu based on specific permissions
  const adminMenu = [];

  // Department management menu - chỉ admin mới thấy
  const departmentPermissions = [
    { permission: "admin", title: "Quản Lý Phòng Ban", href: "/dashboard/departments" },
    { permission: "admin", title: "Thêm Phòng Ban", href: "/dashboard/departments/add" }
  ];

  // User management menu - chỉ hiển thị nếu có ít nhất 1 permission liên quan
  const userPermissions = [
    { permission: "user_view", title: "Quản Lý Người Dùng", href: "/dashboard/user" },
    { permission: "user_add", title: "Thêm Người Dùng", href: "/dashboard/user/add" },
    { permission: "user_import_csv", title: "Nhập File CSV", href: "/dashboard/user/import" }
  ];

  // Department management - chỉ admin
  const availableDepartmentMenus = departmentPermissions.filter(item => hasPermission(item.permission));
  if (availableDepartmentMenus.length > 0) {
    adminMenu.push({
      title: "Phòng Ban",
      href: "/dashboard/departments",
      icon: Shield,
      children: availableDepartmentMenus.map(item => ({
        title: item.title,
        href: item.href
      })),
    });
  }

  const availableUserMenus = userPermissions.filter(item => hasFeaturePermission(item.permission));

  if (availableUserMenus.length > 0) {
    adminMenu.push({
      title: "Thành Viên",
      href: "/dashboard/user",
      icon: Users,
      children: availableUserMenus.map(item => ({
        title: item.title,
        href: item.href
      })),
    });
  }

  // File management menu - chỉ hiển thị nếu có ít nhất 1 permission liên quan
  const filePermissions = [
    { permission: "file_view", title: "Xem File", href: "/dashboard/files" },
    { permission: "file_upload", title: "Upload File", href: "/dashboard/files" },
    { permission: "file_delete", title: "Quản Lý File", href: "/dashboard/files" }
  ];

  const hasAnyFilePermission = filePermissions.some(item => hasFeaturePermission(item.permission));

  if (hasAnyFilePermission) {
    adminMenu.push({
      title: "Quản Lý File",
      href: "/dashboard/files",
      icon: Upload,
      children: [
        { title: "Quản Lý File", href: "/dashboard/files" }
      ],
    });
  }

  // Court case management menu - chỉ hiển thị nếu có ít nhất 1 permission liên quan
  const courtCasePermissions = [
    { permission: "court_case_view", title: "Xem Vụ Việc", href: "/dashboard/court-cases" },
    { permission: "court_case_create", title: "Thêm Vụ Việc", href: "/dashboard/court-cases" },
    { permission: "court_case_edit", title: "Sửa Vụ Việc", href: "/dashboard/court-cases" },
    { permission: "court_case_delete", title: "Xóa Vụ Việc", href: "/dashboard/court-cases" }
  ];

  const hasAnyCourtCasePermission = courtCasePermissions.some(item => hasFeaturePermission(item.permission));

  if (hasAnyCourtCasePermission) {
    adminMenu.push({
      title: "Quản Lý Vụ Việc",
      href: "/dashboard/court-cases",
      icon: FileText,
      children: [
        { title: "Danh Sách Vụ Việc", href: "/dashboard/court-cases" }
      ],
    });
  }

  // System settings menu - chỉ hiển thị nếu có ít nhất 1 permission liên quan
  if (hasFeaturePermission("system_settings_view") || hasFeaturePermission("system_settings_edit")) {
    adminMenu.push({
      title: "Cài đặt",
      href: "/dashboard/setting",
      icon: Settings,
    });
  }
  
  // Dashboard menu - luôn hiển thị "Tổng quan"
  adminMenu.push({
    title: "Tổng quan",
    href: "/dashboard",
    icon: Globe,
  });
  
  const generalMenu = [
    { title: "Chỉnh sửa thông tin", href: "/dashboard/account", icon: User },
  ];

  const userMenu: any[] = [];

  // Build Manager Menu dynamically based on permissions
  const ManagerMenu = [];
  
  // Only add Settings to Manager menu if they have the permission
  if (hasFeaturePermission("system_settings_view") || hasFeaturePermission("system_settings_edit")) {
    ManagerMenu.push({
      title: "Cài đặt",
      href: "/dashboard/manager/setting",
      icon: Settings,
    });
  }
  
  // Only add File Management if they have the permission
  if (hasFeaturePermission("file_view") || hasFeaturePermission("file_upload") || hasFeaturePermission("file_delete")) {
    ManagerMenu.push({
      title: "Quản Lý File",
      href: "/dashboard/files",
      icon: Upload,
    });
  }

  // Only add Court Case Management if they have the permission
  if (hasAnyCourtCasePermission) {
    ManagerMenu.push({
      title: "Quản Lý Vụ Việc",
      href: "/dashboard/court-cases",
      icon: FileText,
    });
  }
  const MenuItem = ({ item, isChild = false }: { item: any, isChild?: boolean }) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActiveRoute(item.href);
    const isExpanded = isMenuExpanded(item.href);

    return (
      <li className={`${isChild ? (isCollapsed ? '' : 'ml-4') : ''}`}>
        <div className="flex items-center">
          {hasChildren && !isCollapsed ? (
            <button
              onClick={() => toggleMenu(item.href)}
              className={`flex items-center w-full px-4 py-3 text-left rounded-lg transition-all duration-200 group ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
              title={isCollapsed ? item.title : ''}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`${isCollapsed ? 'mx-auto' : 'mr-3'} ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              {!isCollapsed && (
                <>
                  <span className="flex-1 font-medium">{item.title}</span>
                  <MenuIcon_Component
                    icon={isExpanded ? ChevronDown : ChevronRight}
                    className={`ml-2 ${isActive ? 'text-white' : 'text-gray-400'}`}
                  />
                </>
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 group ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
              title={isCollapsed ? item.title : ''}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`${isCollapsed ? 'mx-auto' : 'mr-3'} ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              {!isCollapsed && <span className="font-medium">{item.title}</span>}
            </Link>
          )}
        </div>

        {hasChildren && isExpanded && !isCollapsed && (
          <ul className="mt-2 space-y-1 ml-4">
            {item.children.map((child: any) => (
              <li key={child.href}>
                <Link
                  href={child.href}
                  className={`flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${
                    pathname === child.href
                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <span className="w-2 h-2 bg-gray-300 rounded-full mr-3"></span>
                  {child.title}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            closeMobileMenu();
          }}
        />
      )}

      {/* Sidebar */}
      <nav
        className={`
          fixed top-0 bottom-0 left-0 z-50 flex flex-col
          bg-white shadow-xl border-r border-gray-200
          transform transition-all duration-300 ease-in-out
          overflow-hidden
          md:translate-x-0
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          ${isCollapsed ? 'md:w-16' : 'md:w-64'}
          w-64
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b border-gray-200 ${isCollapsed ? 'px-2' : 'px-6'}`}>
          {!isCollapsed && <DashboardLogo />}
          
          {/* Desktop collapse button */}
          <button
            onClick={toggleCollapse}
            className="hidden md:flex p-2 rounded-lg hover:bg-gray-100 transition-colors"
            title={isCollapsed ? 'Mở rộng sidebar' : 'Thu gọn sidebar'}
          >
            <ChevronLeft className={`w-5 h-5 text-gray-500 transition-transform ${isCollapsed ? 'rotate-180' : ''}`} />
          </button>

          {/* Mobile close button */}
          <button
            onClick={closeMobileMenu}
            className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Menu Content */}
        <div className="flex-1 overflow-y-auto py-4 px-4">
          {isAdmin || adminMenu.length > 0 ? (
            <div className="space-y-2">
              {/* Admin/Manager Menu */}
              {adminMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {/* Separator and additional options */}
              <div className={`pt-4 mt-6 border-t border-gray-200 ${isCollapsed ? 'border-t-0 mt-4 pt-2' : ''}`}>
                <MenuItem
                  item={{
                    title: "Thông tin tài khoản",
                    href: "/dashboard/account",
                    icon: User
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {/* General user menu */}
              {generalMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("user") && userMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("manager") && ManagerMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className={`p-4 border-t border-gray-200 ${isCollapsed ? 'px-2' : ''}`}>
          <div className={isCollapsed ? 'flex justify-center' : ''}>
            <ButtonLogout compact={isCollapsed} />
          </div>
        </div>
      </nav>
    </>
  );
}
